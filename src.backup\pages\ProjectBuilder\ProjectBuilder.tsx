import React, { useState } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FolderOpen as FolderIcon,
  Build as BuildIcon,
  Save as SaveIcon,
  PlayArrow as PlayIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { updateProject, createNewProject } from '@/store/slices/projectSlice';
import { startBuild, addBuildLog, updateBuildProgress, completeBuild } from '@/store/slices/buildSlice';
import { addNotification } from '@/store/slices/uiSlice';

const ProjectBuilder: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject, isProjectModified } = useSelector((state: RootState) => state.project);
  const { isBuilding, buildProgress, buildLogs } = useSelector((state: RootState) => state.build);
  
  const [wizardExpanded, setWizardExpanded] = useState(false);
  const [advancedExpanded, setAdvancedExpanded] = useState(false);

  // Initialize project if none exists
  React.useEffect(() => {
    if (!currentProject) {
      const newProject = {
        name: 'New Project',
        scriptPath: '',
        outputPath: '',
        appName: 'MyApp',
        buildMode: 'clean' as const,
        generateMsi: false,
        generateZip: false,
        enableProtection: false,
        wizardMode: false,
        wizardLevel: 'apprentice' as const,
      };
      dispatch(createNewProject(newProject));
    }
  }, [currentProject, dispatch]);

  const handleFieldChange = (field: string, value: any) => {
    if (currentProject) {
      dispatch(updateProject({ [field]: value }));
    }
  };

  const handleBrowseScript = async () => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: ['openFile'],
          filters: [
            { name: 'Python Files', extensions: ['py'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          handleFieldChange('scriptPath', result.filePaths[0]);
          
          // Auto-generate app name from script name
          const scriptName = result.filePaths[0].split(/[\\/]/).pop()?.replace('.py', '') || 'MyApp';
          handleFieldChange('appName', scriptName);
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to browse for script file.',
        }));
      }
    }
  };

  const handleBrowseOutput = async () => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: ['openDirectory'],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          handleFieldChange('outputPath', result.filePaths[0]);
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to browse for output directory.',
        }));
      }
    }
  };

  const handleBrowseIcon = async () => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: ['openFile'],
          filters: [
            { name: 'Icon Files', extensions: ['ico', 'png', 'jpg', 'jpeg'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          handleFieldChange('iconPath', result.filePaths[0]);
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to browse for icon file.',
        }));
      }
    }
  };

  const handleSaveProject = async () => {
    if (!currentProject) return;
    
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showSaveDialog({
          defaultPath: `${currentProject.name}.sbproj`,
          filters: [
            { name: 'SecureBuilder Projects', extensions: ['sbproj'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePath) {
          // TODO: Save project to file
          dispatch(addNotification({
            type: 'success',
            title: 'Project Saved',
            message: `Project saved to ${result.filePath}`,
          }));
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to save project.',
        }));
      }
    }
  };

  const handleBuild = async () => {
    if (!currentProject || !currentProject.scriptPath) {
      dispatch(addNotification({
        type: 'error',
        title: 'Build Error',
        message: 'Please select a Python script to build.',
      }));
      return;
    }

    dispatch(startBuild());
    
    try {
      // Build PyInstaller command
      const args = [
        '--onefile',
        '--name', currentProject.appName,
      ];
      
      if (currentProject.iconPath) {
        args.push('--icon', currentProject.iconPath);
      }
      
      if (currentProject.outputPath) {
        args.push('--distpath', currentProject.outputPath);
      }
      
      args.push(currentProject.scriptPath);
      
      dispatch(updateBuildProgress({
        stage: 'Building',
        progress: 25,
        message: 'Running PyInstaller...',
      }));
      
      if (window.electronAPI) {
        const result = await window.electronAPI.executeCommand('pyinstaller', args);
        
        dispatch(updateBuildProgress({
          stage: 'Finalizing',
          progress: 90,
          message: 'Finalizing build...',
        }));
        
        if (result.code === 0) {
          dispatch(completeBuild({
            success: true,
            projectName: currentProject.name,
            buildMode: currentProject.buildMode,
            outputPath: currentProject.outputPath || 'dist',
          }));
          
          dispatch(addNotification({
            type: 'success',
            title: 'Build Successful',
            message: `${currentProject.appName} built successfully!`,
          }));
        } else {
          throw new Error(result.stderr || 'Build failed');
        }
      }
    } catch (error) {
      dispatch(completeBuild({
        success: false,
        projectName: currentProject.name,
        buildMode: currentProject.buildMode,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
      
      dispatch(addNotification({
        type: 'error',
        title: 'Build Failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      }));
    }
  };

  if (!currentProject) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Project Builder
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={handleSaveProject}
            disabled={!isProjectModified}
          >
            Save Project
          </Button>
          <Button
            variant="contained"
            startIcon={<BuildIcon />}
            onClick={handleBuild}
            disabled={isBuilding || !currentProject.scriptPath}
          >
            {isBuilding ? 'Building...' : 'Build EXE'}
          </Button>
        </Box>
      </Box>

      {isBuilding && buildProgress && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2" gutterBottom>
            {buildProgress.message}
          </Typography>
          <LinearProgress variant="determinate" value={buildProgress.progress} />
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Basic Settings */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basic Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Project Name"
                    value={currentProject.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      fullWidth
                      label="Python Script"
                      value={currentProject.scriptPath}
                      onChange={(e) => handleFieldChange('scriptPath', e.target.value)}
                      placeholder="Select your Python script file"
                    />
                    <Button
                      variant="outlined"
                      startIcon={<FolderIcon />}
                      onClick={handleBrowseScript}
                    >
                      Browse
                    </Button>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="App Name"
                    value={currentProject.appName}
                    onChange={(e) => handleFieldChange('appName', e.target.value)}
                    placeholder="MyApp"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Build Mode</InputLabel>
                    <Select
                      value={currentProject.buildMode}
                      label="Build Mode"
                      onChange={(e) => handleFieldChange('buildMode', e.target.value)}
                    >
                      <MenuItem value="clean">Clean Only</MenuItem>
                      <MenuItem value="protected">Protected</MenuItem>
                      <MenuItem value="obfuscated">Obfuscated</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      fullWidth
                      label="Output Directory"
                      value={currentProject.outputPath}
                      onChange={(e) => handleFieldChange('outputPath', e.target.value)}
                      placeholder="Leave empty for default (dist)"
                    />
                    <Button
                      variant="outlined"
                      startIcon={<FolderIcon />}
                      onClick={handleBrowseOutput}
                    >
                      Browse
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Options */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Options
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={currentProject.generateMsi}
                      onChange={(e) => handleFieldChange('generateMsi', e.target.checked)}
                    />
                  }
                  label="Generate MSI"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={currentProject.generateZip}
                      onChange={(e) => handleFieldChange('generateZip', e.target.checked)}
                    />
                  }
                  label="Generate ZIP"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={currentProject.enableProtection}
                      onChange={(e) => handleFieldChange('enableProtection', e.target.checked)}
                    />
                  }
                  label="Enable Protection"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={currentProject.wizardMode}
                      onChange={(e) => handleFieldChange('wizardMode', e.target.checked)}
                    />
                  }
                  label="Wizard Mode"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Advanced Settings */}
        <Grid item xs={12}>
          <Accordion expanded={advancedExpanded} onChange={() => setAdvancedExpanded(!advancedExpanded)}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Advanced Settings</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <TextField
                      fullWidth
                      label="Icon File"
                      value={currentProject.iconPath || ''}
                      onChange={(e) => handleFieldChange('iconPath', e.target.value)}
                      placeholder="Select icon file (.ico, .png)"
                    />
                    <Button
                      variant="outlined"
                      startIcon={<FolderIcon />}
                      onClick={handleBrowseIcon}
                    >
                      Browse
                    </Button>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Splash Screen"
                    value={currentProject.splashPath || ''}
                    onChange={(e) => handleFieldChange('splashPath', e.target.value)}
                    placeholder="Select splash screen image"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Wizard Settings */}
        {currentProject.wizardMode && (
          <Grid item xs={12}>
            <Accordion expanded={wizardExpanded} onChange={() => setWizardExpanded(!wizardExpanded)}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Wizard Settings</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Wizard Level</InputLabel>
                      <Select
                        value={currentProject.wizardLevel}
                        label="Wizard Level"
                        onChange={(e) => handleFieldChange('wizardLevel', e.target.value)}
                      >
                        <MenuItem value="apprentice">🪄 Apprentice</MenuItem>
                        <MenuItem value="wizard">🎩 Level 3 Wizard</MenuItem>
                        <MenuItem value="mage">🌌 Epic Mage</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <Alert severity="info">
                      <Typography variant="body2">
                        <strong>Apprentice:</strong> Basic build with simple clock example<br/>
                        <strong>Level 3 Wizard:</strong> Advanced settings with protection features<br/>
                        <strong>Epic Mage:</strong> Full feature set with PyArmor and MSI generation
                      </Typography>
                    </Alert>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default ProjectBuilder;
