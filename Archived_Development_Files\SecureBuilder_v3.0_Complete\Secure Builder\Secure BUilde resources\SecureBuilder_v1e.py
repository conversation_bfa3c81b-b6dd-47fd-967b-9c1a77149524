import customtkinter as ctk
import os, subprocess, zipfile
from tkinter import filedialog
class SecureBuilderApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("SecureBuilder v1e")
        self.geometry("700x400")
        self.file_path = ctk.StringVar()
        self.pyarmor = ctk.BooleanVar(value=False)
        self.unlock_wrapper = ctk.BooleanVar(value=False)
        self.zip_output = ctk.BooleanVar(value=True)

        ctk.CTkLabel(self, text="Select Python Script to Package").pack(pady=10)
        ctk.CTkEntry(self, textvariable=self.file_path, width=400).pack()
        ctk.CTkButton(self, text="Browse", command=self.browse).pack()

        ctk.CTkCheckBox(self, text="Apply PyArmor Obfuscation", variable=self.pyarmor).pack(pady=5)
        ctk.CTkCheckBox(self, text="Apply Unlock Wrapper", variable=self.unlock_wrapper).pack(pady=5)
        ctk.CTkCheckBox(self, text="Create Output ZIP Package", variable=self.zip_output).pack(pady=5)
        ctk.CTkButton(self, text="BUILD", command=self.build).pack(pady=20)

    def browse(self):
        path = filedialog.askopenfilename(filetypes=[("Python files", "*.py")])
        if path:
            self.file_path.set(path)

    def build(self):
        script = self.file_path.get()
        if not os.path.isfile(script):
            print("Invalid file")
            return
        if self.pyarmor.get():
            subprocess.call(f"pyarmor obfuscate {script} -O obf", shell=True)
            script = os.path.join("obf", os.path.basename(script))
        build_cmd = ["pyinstaller", "--onefile", script]
        if self.unlock_wrapper.get():
            with open("wrap_temp.py", "w") as f:
                f.write("input('Enter Unlock Code: ')
")
                f.write(f"import subprocess; subprocess.call(r'{script}')
")
            build_cmd = ["pyinstaller", "--onefile", "wrap_temp.py", "-n", "wrapped_app"]
        subprocess.call(build_cmd)
        if self.zip_output.get():
            dist = "dist"
            output = os.path.join(dist, "wrapped_app.exe" if self.unlock_wrapper.get() else os.path.basename(script).replace(".py", ".exe"))
            with zipfile.ZipFile("output_package.zip", "w") as z:
                z.write(output, arcname=os.path.basename(output))
        print("Build complete.")
if __name__ == "__main__":
    app = SecureBuilderApp()
    app.mainloop()
