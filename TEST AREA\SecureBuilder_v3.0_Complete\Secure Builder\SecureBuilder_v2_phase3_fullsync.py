import os
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import json
import subprocess
import zipfile
import tempfile
import datetime

PROFILE_EXT = ".sbproj"
SECURE_BACKUP_DIR = os.path.join(os.path.expanduser("~"), "SecureBuilder_Backups")

class SecureBuilderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SecureBuilder v2")
        self.script_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.app_name = tk.StringVar()
        self.splash_path = tk.StringVar()
        self.icon_path = tk.StringVar()
        self.sound_path = tk.StringVar()
        self.build_mode = tk.StringVar(value="Clean Only")
        self.protect_enabled = tk.BooleanVar(value=False)
        self.protected_output_mode = tk.StringVar(value="Secure Only")
        self.generate_msi_var = tk.BooleanVar(value=False)
        self.dev_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_level = tk.StringVar(value="Apprentice")
        self.wizard_label_var = tk.StringVar()

        self.create_widgets()

    def create_widgets(self):
        frame = ctk.CTkFrame(self.root)
        frame.pack(padx=20, pady=20)

        padding = dict(padx=5, pady=4)

        ctk.CTkCheckBox(frame, text="Enable Wizard Mode", variable=self.wizard_mode_enabled).grid(row=15, column=1, sticky="w", pady=6)
        ctk.CTkLabel(frame, text="Wizard Level:").grid(row=16, column=0, sticky="w", **padding)
        ctk.CTkOptionMenu(frame, variable=self.wizard_level, values=["Apprentice", "Level 3 Wizard", "Epic Mage"]).grid(row=16, column=1, **padding)

        def update_wizard_icon():
            icon_map = {
                "Apprentice": "🪄 Begin Wizard",
                "Level 3 Wizard": "🎩 Begin Wizard",
                "Epic Mage": "🌌 Begin Wizard"
            }
            self.wizard_label_var.set(icon_map.get(self.wizard_level.get(), "🪄 Begin Wizard"))

        update_wizard_icon()
        self.wizard_level.trace_add("write", lambda *args: update_wizard_icon())
        ctk.CTkButton(frame, textvariable=self.wizard_label_var, command=self.start_wizard).grid(row=17, column=1, sticky="w", pady=6)

    def start_wizard(self):
        level = self.wizard_level.get()
        if level == "Apprentice":
            clock_code = '''
import time
while True:
    print("\033[92m" + time.strftime("%H:%M:%S"), end="\r")
    time.sleep(1)
'''
            timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            wizard_temp = tempfile.gettempdir()
            apprentice_script = os.path.join(wizard_temp, f"digital_clock_{timestamp}.py")
            with open(apprentice_script, "w") as f:
                f.write(clock_code)
            self.script_path.set(apprentice_script)
            self.app_name.set("DigitalClock")
            self.build_mode.set("Clean Only")
            messagebox.showinfo("Wizard Step 1", "Clock script created. Pre-filled Script Path and App Name.")
            messagebox.showinfo("Wizard Step 2", "Please click 'Build EXE' to compile your first console clock app.")
        elif level == "Level 3 Wizard":
            messagebox.showinfo("Level 3 Wizard", "This tier will prefill advanced settings like splash, icon, and zip handling. Coming soon.")
        elif level == "Epic Mage":
            messagebox.showinfo("Epic Mage", "The ultimate tier — with PyArmor, unlock triggers, and CLI export. Not yet unlocked.")

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    root = ctk.CTk()
    app = SecureBuilderApp(root)
    root.mainloop()

    def build_exe(self):
            script = self.script_path.get()
            appname = self.app_name.get()
            output_dir = self.output_folder.get()
            if not script or not appname:
                messagebox.showwarning("Missing Info", "Script path and App Name are required.")
                return

            build_cmd = ["pyinstaller", "--onefile", "--name", appname, script]
            if self.icon_path.get():
                build_cmd += ["--icon", self.icon_path.get()]
            if self.splash_path.get():
                build_cmd += ["--splash", self.splash_path.get()]

            subprocess.run(build_cmd)
            messagebox.showinfo("Build Complete", "Build finished. Check the 'dist' folder.")

    def save_profile(self):
        profile = {
            "script_path": self.script_path.get(),
            "output_folder": self.output_folder.get(),
            "app_name": self.app_name.get(),
            "icon_path": self.icon_path.get(),
            "splash_path": self.splash_path.get(),
            "sound_path": self.sound_path.get(),
            "build_mode": self.build_mode.get(),
            "protect_enabled": self.protect_enabled.get(),
            "protected_output_mode": self.protected_output_mode.get(),
            "generate_msi": self.generate_msi_var.get(),
            "dev_mode_enabled": self.dev_mode_enabled.get(),
            "wizard_mode_enabled": self.wizard_mode_enabled.get(),
            "wizard_level": self.wizard_level.get()
        }
        file_path = filedialog.asksaveasfilename(defaultextension=PROFILE_EXT, filetypes=[("SecureBuilder Profiles", "*.sbproj")])
        if file_path:
            with open(file_path, "w") as f:
                json.dump(profile, f, indent=2)
            messagebox.showinfo("Saved", f"Profile saved to {file_path}")

    def load_profile(self):
        file_path = filedialog.askopenfilename(filetypes=[("SecureBuilder Profiles", "*.sbproj")])
        if file_path:
            with open(file_path, "r") as f:
                data = json.load(f)
            self.script_path.set(data.get("script_path", ""))
            self.output_folder.set(data.get("output_folder", ""))
            self.app_name.set(data.get("app_name", ""))
            self.icon_path.set(data.get("icon_path", ""))
            self.splash_path.set(data.get("splash_path", ""))
            self.sound_path.set(data.get("sound_path", ""))
            self.build_mode.set(data.get("build_mode", "Clean Only"))
            self.protect_enabled.set(data.get("protect_enabled", False))
            self.protected_output_mode.set(data.get("protected_output_mode", "Secure Only"))
            self.generate_msi_var.set(data.get("generate_msi", False))
            self.dev_mode_enabled.set(data.get("dev_mode_enabled", False))
            self.wizard_mode_enabled.set(data.get("wizard_mode_enabled", False))
            self.wizard_level.set(data.get("wizard_level", "Apprentice"))