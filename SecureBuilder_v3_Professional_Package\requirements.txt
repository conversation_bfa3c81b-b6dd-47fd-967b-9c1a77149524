# SecureBuilder v3.0 Professional - Requirements
# ============================================

# Core GUI Framework
customtkinter>=5.2.0

# Drag and Drop Support
tkinterdnd2>=0.3.0

# System Monitoring (for test projects)
psutil>=5.9.0

# Build Tool
pyinstaller>=6.0.0

# Optional: Code Obfuscation
# pyarmor>=8.0.0

# Optional: Additional Build Tools
# auto-py-to-exe>=2.0.0
# cx-Freeze>=6.0.0
# nuitka>=1.8.0

# Note: UPX is a separate download from https://upx.github.io/
