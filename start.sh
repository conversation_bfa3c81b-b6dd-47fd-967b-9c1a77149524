#!/bin/bash

echo ""
echo "========================================"
echo "   SecureBuilder v3.0 - Quick Start"
echo "========================================"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "[ERROR] Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    echo ""
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "[ERROR] npm is not available"
    echo "Please reinstall Node.js from https://nodejs.org/"
    echo ""
    exit 1
fi

echo "[INFO] Node.js and npm are available"
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "[INFO] Installing dependencies..."
    echo "This may take a few minutes on first run..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "[ERROR] Failed to install dependencies"
        echo ""
        exit 1
    fi
    echo ""
    echo "[SUCCESS] Dependencies installed successfully!"
    echo ""
else
    echo "[INFO] Dependencies already installed"
    echo ""
fi

# Check if Python is available
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "[WARNING] Python is not installed or not in PATH"
    echo "SecureBuilder needs Python for building executables"
    echo "Please install Python from https://python.org/"
    echo ""
fi

# Check if PyInstaller is available
if ! command -v pyinstaller &> /dev/null; then
    echo "[WARNING] PyInstaller is not installed"
    echo "Run: pip install pyinstaller"
    echo ""
fi

echo "[INFO] Starting SecureBuilder v3.0..."
echo ""
echo "The application will open in a new window"
echo "Press Ctrl+C to stop the development server"
echo ""

# Start the development server
npm run dev

echo ""
echo "[INFO] SecureBuilder v3.0 has been stopped"
