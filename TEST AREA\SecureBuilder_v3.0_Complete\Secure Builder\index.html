<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SecureBuilder</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
        background-color: #121212;
        color: #ffffff;
        overflow: hidden;
      }
      
      #root {
        height: 100vh;
        width: 100vw;
      }
      
      /* Loading screen */
      .loading-screen {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      }
      
      .loading-logo {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 2rem;
        background: linear-gradient(45deg, #90caf9, #f48fb1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #90caf9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-top: 1rem;
        font-size: 1.1rem;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-screen">
        <div class="loading-logo">🔒 SecureBuilder</div>
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading application...</div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
