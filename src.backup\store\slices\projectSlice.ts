import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ProjectConfig {
  id: string;
  name: string;
  scriptPath: string;
  outputPath: string;
  appName: string;
  iconPath?: string;
  splashPath?: string;
  buildMode: 'clean' | 'protected' | 'obfuscated';
  generateMsi: boolean;
  generateZip: boolean;
  enableProtection: boolean;
  wizardMode: boolean;
  wizardLevel: 'apprentice' | 'wizard' | 'mage';
  createdAt: string;
  updatedAt: string;
}

interface ProjectState {
  currentProject: ProjectConfig | null;
  recentProjects: ProjectConfig[];
  isProjectModified: boolean;
  projectPath?: string;
}

const initialState: ProjectState = {
  currentProject: null,
  recentProjects: [],
  isProjectModified: false,
};

const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setCurrentProject: (state, action: PayloadAction<ProjectConfig>) => {
      state.currentProject = action.payload;
      state.isProjectModified = false;
    },
    updateProject: (state, action: PayloadAction<Partial<ProjectConfig>>) => {
      if (state.currentProject) {
        state.currentProject = {
          ...state.currentProject,
          ...action.payload,
          updatedAt: new Date().toISOString(),
        };
        state.isProjectModified = true;
      }
    },
    createNewProject: (state, action: PayloadAction<Omit<ProjectConfig, 'id' | 'createdAt' | 'updatedAt'>>) => {
      const now = new Date().toISOString();
      state.currentProject = {
        ...action.payload,
        id: `project_${Date.now()}`,
        createdAt: now,
        updatedAt: now,
      };
      state.isProjectModified = true;
    },
    addToRecentProjects: (state, action: PayloadAction<ProjectConfig>) => {
      const existingIndex = state.recentProjects.findIndex(p => p.id === action.payload.id);
      if (existingIndex >= 0) {
        state.recentProjects.splice(existingIndex, 1);
      }
      state.recentProjects.unshift(action.payload);
      state.recentProjects = state.recentProjects.slice(0, 10); // Keep only 10 recent projects
    },
    setProjectPath: (state, action: PayloadAction<string>) => {
      state.projectPath = action.payload;
    },
    markProjectSaved: (state) => {
      state.isProjectModified = false;
    },
    clearCurrentProject: (state) => {
      state.currentProject = null;
      state.isProjectModified = false;
      state.projectPath = undefined;
    },
  },
});

export const {
  setCurrentProject,
  updateProject,
  createNewProject,
  addToRecentProjects,
  setProjectPath,
  markProjectSaved,
  clearCurrentProject,
} = projectSlice.actions;

export default projectSlice.reducer;
