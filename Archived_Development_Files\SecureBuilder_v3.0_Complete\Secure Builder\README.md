# SecureBuilder v3.0

Modern SecureBuilder - Safe, verifiable, and reversible Python executable builder with economic configuration management.

## Features

### 🔧 Core Build Features
- **PyInstaller Integration**: Modern wrapper with real-time progress tracking
- **Project Management**: Save/load profiles with version control
- **Build Pipeline**: Support for multiple build modes (Clean, Protected, MSI, ZIP)
- **Wizard System**: Progressive disclosure UI for different skill levels
- **Asset Management**: Icon, splash screen, and resource handling

### 🔒 Advanced Security Features
- **PyArmor Integration**: Code obfuscation with configurable settings
- **Unlock System**: Secure wrapper generation with key management
- **Digital Signing**: Certificate-based signing for executables
- **Audit Trail**: Complete build history and logging

### ⚙️ Economic Configuration System
- **PatchDelta System**: Reversible configuration changes
- **Schema Validation**: Rule-based validation for economic configs
- **F.I.S.H. Integration**: Flexible, Intelligent, Safe, Harmonious rule application system
- **Change History**: Undo/redo with complete state management
- **Export System**: Multiple export formats (.json, .xbuild, etc.)

## Technology Stack

- **Frontend**: React 18 + TypeScript + Material-UI
- **Desktop**: Electron 28
- **State Management**: Redux Toolkit
- **Build Tool**: Vite
- **Styling**: Material-UI with dark theme
- **Testing**: Vitest

## Prerequisites

- Node.js 18+ 
- Python 3.8+ with PyInstaller
- Git

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd secure-builder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Python dependencies**
   ```bash
   pip install pyinstaller pyarmor
   ```

## Development

1. **Start development server**
   ```bash
   npm run dev
   ```
   This will start both the React dev server and Electron in development mode.

2. **Build for production**
   ```bash
   npm run build
   ```

3. **Package for distribution**
   ```bash
   # Windows
   npm run package:win
   
   # macOS
   npm run package:mac
   
   # Linux
   npm run package:linux
   ```

## Project Structure

```
secure-builder/
├── electron/                 # Electron main process
│   ├── main.ts              # Main Electron process
│   └── preload.ts           # Preload script for IPC
├── src/                     # React frontend
│   ├── components/          # Reusable UI components
│   ├── pages/              # Main application pages
│   ├── store/              # Redux store and slices
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Utility functions
├── dist/                   # Build output
└── release/               # Packaged applications
```

## Usage

### Creating a New Project

1. Click "New Project" on the Dashboard
2. Configure your Python script and build settings
3. Choose build mode (Clean, Protected, or Obfuscated)
4. Click "Build EXE" to create your executable

### Wizard Mode

SecureBuilder includes a wizard system with three levels:

- **🪄 Apprentice**: Basic build with simple examples
- **🎩 Level 3 Wizard**: Advanced settings with protection features  
- **🌌 Epic Mage**: Full feature set with PyArmor and MSI generation

### Configuration Management

The Config Manager allows you to:

- Load and edit economic configuration files
- Apply reversible patches with the PatchDelta system
- Use F.I.S.H. rules for automated validation
- Track complete change history with undo/redo

## Configuration

Settings can be configured in the Settings page:

- **Theme**: Light or Dark mode
- **Build Tools**: Paths to PyInstaller and PyArmor
- **Developer Info**: Name for patch signatures
- **Logging**: Debug and telemetry options

## Build Modes

### Clean Only
Basic PyInstaller build with no additional protection.

### Protected
Adds unlock wrapper system with key-based access control.

### Obfuscated  
Uses PyArmor for code obfuscation and advanced protection.

## F.I.S.H. Rules Engine

The Flexible, Intelligent, Safe, Harmonious (F.I.S.H.) rules engine provides:

- **Validation Rules**: Ensure configuration values are within acceptable ranges
- **Transformation Rules**: Apply automated changes based on conditions
- **Safety Checks**: Prevent dangerous or invalid configurations
- **Audit Trail**: Track all rule applications and changes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review existing issues for solutions
