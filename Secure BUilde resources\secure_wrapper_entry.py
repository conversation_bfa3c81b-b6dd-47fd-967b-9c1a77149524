
import os, sys, hashlib, uuid, subprocess

APP_NAME = "WrappedApp"
TOKEN_FILE = "token.txt"

def expected_token():
    return hashlib.sha256(f"{uuid.getnode()}-{APP_NAME}".encode()).hexdigest()[:32]

def main():
    if not os.path.exists(TOKEN_FILE):
        print("🔒 Token missing.")
        sys.exit(1)
    token = open(TOKEN_FILE).read().strip()
    if token != expected_token():
        print("❌ Invalid token.")
        sys.exit(1)
    exe = APP_NAME + ".exe"
    if os.path.exists(exe):
        subprocess.call(exe)
    else:
        print("⚠️ Executable not found.")

if __name__ == "__main__":
    main()
