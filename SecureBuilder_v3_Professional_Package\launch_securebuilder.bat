@echo off
title SecureBuilder v3.0 Professional Launcher
echo.
echo ================================================================
echo  🛡️ SecureBuilder v3.0 Professional
echo ================================================================
echo.
echo Starting SecureBuilder Professional GUI...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org/
    pause
    exit /b 1
)

REM Check if CustomTkinter is installed
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing CustomTkinter...
    pip install customtkinter
    if errorlevel 1 (
        echo ❌ Failed to install CustomTkinter
        pause
        exit /b 1
    )
)

REM Launch the application
echo ✅ Launching SecureBuilder Professional...
python SecureBuilder_v3_Professional.py

if errorlevel 1 (
    echo.
    echo ❌ Application encountered an error
    pause
)

echo.
echo 👋 SecureBuilder closed
pause
