# 🛡️ SecureBuilder v3.0 Professional

## Modern CustomTkinter GUI for Python Executable Building

### 🎯 Overview

SecureBuilder v3.0 Professional is a modern, user-friendly GUI application built with CustomTkinter that simplifies the process of converting Python scripts into standalone executables using PyInstaller. It features a professional dark/light theme interface, project management, and advanced configuration options.

### ✨ Features

#### 🎨 Modern Interface
- **Professional Design**: Clean, modern interface with CustomTkinter
- **Dark/Light Themes**: Switch between dark, light, and system themes
- **Responsive Layout**: Adaptive interface that works on different screen sizes
- **Tabbed Navigation**: Organized interface with sidebar navigation

#### 🔧 Core Functionality
- **PyInstaller Integration**: Full PyInstaller command-line integration
- **One-File Builds**: Create single executable files
- **Icon Support**: Add custom icons to your executables
- **Console Options**: Choose between console and windowed applications
- **Output Management**: Organized output directory structure

#### 📁 Project Management
- **Save/Load Projects**: Save your build configurations as `.sbproj` files
- **Project Templates**: Quick setup for common project types
- **Settings Persistence**: Remember your preferences between sessions

#### ⚙️ Advanced Options
- **Hidden Imports**: Specify modules that PyInstaller might miss
- **Exclude Modules**: Exclude unnecessary modules to reduce size
- **Data Files**: Include additional data files in your build
- **Custom Output**: Specify custom output directories
- **Build Logging**: Comprehensive build output and error reporting

#### 🚀 Build Features
- **Real-time Output**: Live build progress and output streaming
- **Error Handling**: Clear error messages and troubleshooting tips
- **ZIP Archives**: Automatically create ZIP packages of builds
- **Build History**: Track your build history and results

### 🚀 Quick Start

#### Prerequisites
- Python 3.8 or higher
- PyInstaller (`pip install pyinstaller`)
- CustomTkinter (`pip install customtkinter`)

#### Installation
1. Download `SecureBuilder_v3_Professional.py`
2. Install dependencies:
   ```bash
   pip install customtkinter pyinstaller
   ```
3. Run the application:
   ```bash
   python SecureBuilder_v3_Professional.py
   ```

#### Or use the launcher:
1. Double-click `launch_securebuilder.bat` (Windows)
2. The launcher will check dependencies and start the application

### 📖 Usage Guide

#### Basic Workflow
1. **Select Script**: Browse and select your Python script
2. **Configure Output**: Set executable name and output directory
3. **Choose Options**: Select build options (one-file, console, etc.)
4. **Build**: Click "Quick Build" to create your executable

#### Project Management
1. **Save Project**: Save your current configuration as a `.sbproj` file
2. **Load Project**: Load a previously saved project configuration
3. **New Project**: Clear all settings and start fresh

#### Advanced Configuration
1. **Hidden Imports**: Add modules that PyInstaller might miss
   - Format: `module1,module2,module3`
   - Example: `requests,numpy,matplotlib`

2. **Exclude Modules**: Remove unnecessary modules
   - Format: `module1,module2,module3`
   - Example: `tkinter,unittest,doctest`

3. **Data Files**: Include additional files
   - Format: `source:destination`
   - Example: `data/*:data/` or `config.ini:.`

### 🎨 Interface Guide

#### Sidebar Navigation
- **📁 Project**: Main project configuration
- **⚙️ Build Settings**: Basic build options
- **🔧 Advanced**: Advanced PyInstaller options
- **📊 Output**: Build output and logs

#### Quick Actions
- **🚀 Quick Build**: Start build with current settings
- **Theme Toggle**: Switch between dark/light themes

#### Status Bar
- **Status**: Current application status
- **Progress**: Build progress indicator

### 🔧 Configuration Options

#### Basic Options
- **One File**: Create a single executable file
- **Show Console**: Show/hide console window
- **Create ZIP**: Automatically create ZIP archive

#### Advanced Options
- **PyArmor Obfuscation**: Code obfuscation (requires PyArmor)
- **UPX Compression**: Executable compression (requires UPX)

### 📁 File Structure

```
SecureBuilder_Professional/
├── SecureBuilder_v3_Professional.py    # Main application
├── launch_securebuilder.bat           # Windows launcher
├── securebuilder_settings.json        # User settings (auto-created)
├── *.sbproj                           # Project files
└── dist/                              # Default output directory
```

### 🎯 Project File Format

SecureBuilder projects are saved as JSON files with the `.sbproj` extension:

```json
{
  "script_path": "path/to/script.py",
  "exe_name": "MyApplication",
  "use_onefile": true,
  "use_console": false,
  "hidden_imports": "requests,numpy",
  "exclude_modules": "tkinter,unittest",
  "created": "2024-12-09T15:30:00",
  "version": "3.0"
}
```

### 🐛 Troubleshooting

#### Common Issues

**Application won't start:**
- Ensure Python 3.8+ is installed
- Install CustomTkinter: `pip install customtkinter`
- Check for error messages in terminal

**Build fails:**
- Check that PyInstaller is installed: `pip install pyinstaller`
- Verify script path is correct
- Check build output for specific errors
- Try adding missing modules to "Hidden Imports"

**Missing modules in executable:**
- Add module names to "Hidden Imports"
- Use `--hidden-import` flag manually if needed

**Large executable size:**
- Add unnecessary modules to "Exclude Modules"
- Consider using UPX compression
- Use `--onefile` option

### 🔄 Updates & Versions

#### Version 3.0 Professional
- Modern CustomTkinter interface
- Project management system
- Advanced PyInstaller options
- Real-time build output
- Theme switching
- Settings persistence

#### Planned Features
- Plugin system for custom build steps
- Template library for common project types
- Cloud build integration
- Multi-platform build support
- Automated testing integration

### 🤝 Contributing

This is part of the SecureBuilder project evolution:
- v1.x: Basic PyInstaller wrapper
- v2.x: Enhanced CLI with options
- v3.0: Professional GUI with project management

### 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review build output for specific errors
3. Ensure all dependencies are installed
4. Check PyInstaller documentation for advanced options

### 🏆 Advantages over Command Line

- **User-Friendly**: No need to remember PyInstaller commands
- **Visual Feedback**: Real-time build progress and output
- **Project Management**: Save and reuse configurations
- **Error Prevention**: GUI validation prevents common mistakes
- **Professional Appearance**: Modern, clean interface
- **Cross-Platform**: Works on Windows, macOS, and Linux

---

**🎉 Enjoy building professional Python executables with SecureBuilder v3.0 Professional!**
