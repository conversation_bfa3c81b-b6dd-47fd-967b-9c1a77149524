# 🔧 SecureBuilder v3.0 - Troubleshooting Guide

## 🚨 Launch Crashes & Debugging

### **Comprehensive Logging System**

SecureBuilder v3.0 now includes extensive logging to help diagnose launch issues:

#### **Log Locations:**
- **Electron Main Process**: `%USERPROFILE%\SecureBuilder\logs\main-YYYY-MM-DD.log` (Windows)
- **React App**: Browser console + localStorage
- **Debug Panel**: Press `Ctrl+Shift+D` in the app to view all logs

#### **Log Levels:**
- 🔍 **DEBUG**: Detailed execution flow
- ℹ️ **INFO**: General application events  
- ⚠️ **WARN**: Potential issues
- 🚨 **ERROR**: Critical failures

### **Common Launch Issues**

#### **1. "npm not found" or "Node.js not installed"**
```bash
# Check if Node.js is installed
node --version
npm --version

# If not installed:
# Download from https://nodejs.org/
# Choose LTS version (18.x or higher)
```

#### **2. "Port 5173 already in use"**
```bash
# Kill process using the port
npx kill-port 5173

# Or find and kill manually (Windows)
netstat -ano | findstr :5173
taskkill /PID <PID_NUMBER> /F

# Or change port in vite.config.ts
```

#### **3. "Failed to install dependencies"**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json  # macOS/Linux
rmdir /s node_modules & del package-lock.json  # Windows

npm cache clean --force
npm install
```

#### **4. "Electron failed to start"**
```bash
# Check Electron installation
npm list electron

# Reinstall Electron
npm uninstall electron
npm install electron --save-dev
```

#### **5. "React app failed to load"**
```bash
# Build React app first
npm run build:react

# Check for TypeScript errors
npm run lint

# Try production build
npm run build
```

### **Debug Panel Usage**

The built-in debug panel provides real-time diagnostics:

1. **Open Debug Panel**: 
   - Press `Ctrl+Shift+D` in the app
   - Or click the 🐛 bug icon in the toolbar

2. **Features**:
   - View all logs with filtering
   - Export logs to file
   - Real-time log updates
   - System information display

3. **Log Filtering**:
   - Filter by level (DEBUG, INFO, WARN, ERROR)
   - Search by message or component
   - View last 100 entries

### **Step-by-Step Debugging**

#### **Step 1: Check Prerequisites**
```bash
# Verify Node.js (18+)
node --version

# Verify npm
npm --version

# Verify Python (optional, for building)
python --version

# Verify PyInstaller (optional)
pyinstaller --version
```

#### **Step 2: Clean Installation**
```bash
# Navigate to project directory
cd path/to/SecureBuilder_v3.0

# Clean install
rm -rf node_modules package-lock.json
npm install
```

#### **Step 3: Check Build Process**
```bash
# Test TypeScript compilation
npm run build:electron

# Test React build
npm run build:react

# Full build test
npm run build
```

#### **Step 4: Launch with Logging**
```bash
# Windows
start.bat

# macOS/Linux
./start.sh

# Manual launch with verbose output
npm run dev --verbose
```

#### **Step 5: Check Logs**
1. **Electron Logs**: Check `%USERPROFILE%\SecureBuilder\logs\`
2. **Browser Console**: Open DevTools (F12) and check Console tab
3. **Debug Panel**: Press `Ctrl+Shift+D` in the app

### **Error Patterns & Solutions**

#### **Pattern: "Cannot resolve module"**
```
ERROR: Cannot resolve module '@/components/...'
```
**Solution**: Check TypeScript path mapping in `tsconfig.json`

#### **Pattern: "Electron security warning"**
```
WARN: Electron Security Warning
```
**Solution**: Normal in development mode, ignored in production

#### **Pattern: "Failed to load resource"**
```
ERROR: Failed to load resource: net::ERR_CONNECTION_REFUSED
```
**Solution**: React dev server not running, check port 5173

#### **Pattern: "Module not found"**
```
ERROR: Module not found: Can't resolve 'electron-store'
```
**Solution**: Dependency missing, run `npm install`

### **Advanced Debugging**

#### **Enable Verbose Logging**
Add to `package.json` scripts:
```json
{
  "dev:debug": "cross-env DEBUG=* npm run dev",
  "electron:debug": "cross-env ELECTRON_ENABLE_LOGGING=1 npm run dev:electron"
}
```

#### **Chrome DevTools for Electron**
1. Start app with `npm run dev`
2. In Electron window, press `Ctrl+Shift+I`
3. Check Console, Network, and Sources tabs

#### **React DevTools**
1. Install React DevTools browser extension
2. Open in development mode
3. Inspect component tree and state

### **Performance Issues**

#### **Slow Startup**
- Check antivirus software (may scan node_modules)
- Use SSD storage for better I/O performance
- Close unnecessary applications

#### **High Memory Usage**
- Normal in development mode (React hot reload)
- Production builds use less memory
- Check for memory leaks in Debug Panel

### **Platform-Specific Issues**

#### **Windows**
- **Antivirus**: May block Electron executable
- **PowerShell**: May need execution policy change
- **Path Issues**: Ensure Node.js is in PATH

#### **macOS**
- **Gatekeeper**: May block unsigned Electron app
- **Permissions**: May need accessibility permissions
- **Rosetta**: M1 Macs may need Rosetta for some dependencies

#### **Linux**
- **Dependencies**: May need additional system libraries
- **Permissions**: Ensure execute permissions on scripts
- **Display**: Check X11/Wayland compatibility

### **Getting Help**

#### **Information to Collect**
1. **System Info**: OS, Node.js version, npm version
2. **Error Logs**: From Debug Panel and console
3. **Steps to Reproduce**: Exact sequence that causes the issue
4. **Environment**: Development vs production, first run vs subsequent

#### **Log Export**
1. Open Debug Panel (`Ctrl+Shift+D`)
2. Click "Export Logs" button
3. Attach the exported file when reporting issues

#### **Quick Fixes Checklist**
- [ ] Node.js 18+ installed
- [ ] `npm install` completed successfully
- [ ] Port 5173 is available
- [ ] No TypeScript compilation errors
- [ ] Antivirus not blocking the app
- [ ] Latest version of dependencies

### **Emergency Recovery**

If the app completely fails to start:

1. **Reset to defaults**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

2. **Check system requirements**:
   - Node.js 18+
   - 4GB+ RAM
   - 1GB+ free disk space

3. **Try minimal launch**:
   ```bash
   npm run build:react
   npm run build:electron
   npm start
   ```

4. **Last resort - browser mode**:
   ```bash
   npm run dev:react
   # Open http://localhost:5173 in browser
   ```

---

**Remember**: The Debug Panel (`Ctrl+Shift+D`) is your best friend for diagnosing issues! 🐛
