import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>ton, Card, CardContent, Alert } from '@mui/material';
import { logger } from '@/utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    logger.error('Error boundary caught error', error, 'ErrorBoundary');
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    logger.error('Error boundary componentDidCatch', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    }, 'ErrorBoundary');
    
    this.setState({ error, errorInfo });
  }

  handleReload = () => {
    logger.info('User clicked reload after error', {}, 'ErrorBoundary');
    window.location.reload();
  };

  handleReset = () => {
    logger.info('User clicked reset after error', {}, 'ErrorBoundary');
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  exportLogs = () => {
    try {
      const logs = logger.exportLogs();
      const blob = new Blob([logs], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `securebuilder-logs-${new Date().toISOString().slice(0, 19)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      logger.info('Logs exported successfully', {}, 'ErrorBoundary');
    } catch (error) {
      logger.error('Failed to export logs', error, 'ErrorBoundary');
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            p: 3,
            backgroundColor: '#121212',
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent>
              <Alert severity="error" sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  🚨 Application Error
                </Typography>
                <Typography variant="body2">
                  SecureBuilder encountered an unexpected error and needs to restart.
                </Typography>
              </Alert>

              <Typography variant="h6" gutterBottom>
                Error Details:
              </Typography>
              
              <Box
                sx={{
                  backgroundColor: '#1e1e1e',
                  p: 2,
                  borderRadius: 1,
                  mb: 3,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  overflow: 'auto',
                  maxHeight: 200,
                }}
              >
                <Typography variant="body2" color="error">
                  {this.state.error?.message || 'Unknown error'}
                </Typography>
                {this.state.error?.stack && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: '0.75rem' }}>
                    {this.state.error.stack}
                  </Typography>
                )}
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Component Stack:
              </Typography>
              <Box
                sx={{
                  backgroundColor: '#1e1e1e',
                  p: 2,
                  borderRadius: 1,
                  mb: 3,
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  overflow: 'auto',
                  maxHeight: 150,
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {this.state.errorInfo?.componentStack || 'No component stack available'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button variant="contained" onClick={this.handleReload}>
                  Reload Application
                </Button>
                <Button variant="outlined" onClick={this.handleReset}>
                  Try Again
                </Button>
                <Button variant="outlined" onClick={this.exportLogs}>
                  Export Logs
                </Button>
              </Box>

              <Alert severity="info" sx={{ mt: 3 }}>
                <Typography variant="body2">
                  <strong>Troubleshooting Tips:</strong>
                  <br />
                  • Check the browser console for additional errors
                  • Ensure Node.js and Python are properly installed
                  • Try clearing browser cache and restarting
                  • Export logs and check for specific error patterns
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
