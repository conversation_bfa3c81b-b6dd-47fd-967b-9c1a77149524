import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Economic configuration types (from deep dive document)
export interface PatchDelta {
  id: string;
  target: string;
  field: string;
  before: any;
  after: any;
  timestamp: string;
  user: string;
  reason: string;
}

export interface EconomicConfig {
  id: string;
  name: string;
  version: string;
  data: Record<string, any>;
  schema?: Record<string, any>;
  lastModified: string;
}

interface ConfigState {
  currentConfig: EconomicConfig | null;
  patchHistory: PatchDelta[];
  undoStack: PatchDelta[];
  redoStack: PatchDelta[];
  isConfigModified: boolean;
  validationErrors: Array<{
    field: string;
    message: string;
    severity: 'error' | 'warning';
  }>;
  fishRules: Array<{
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    conditions: Record<string, any>;
    actions: Record<string, any>;
  }>;
}

const initialState: ConfigState = {
  currentConfig: null,
  patchHistory: [],
  undoStack: [],
  redoStack: [],
  isConfigModified: false,
  validationErrors: [],
  fishRules: [],
};

const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    loadConfig: (state, action: PayloadAction<EconomicConfig>) => {
      state.currentConfig = action.payload;
      state.isConfigModified = false;
      state.patchHistory = [];
      state.undoStack = [];
      state.redoStack = [];
      state.validationErrors = [];
    },
    applyPatch: (state, action: PayloadAction<Omit<PatchDelta, 'id' | 'timestamp'>>) => {
      const patch: PatchDelta = {
        ...action.payload,
        id: `patch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };

      // Apply the patch to current config
      if (state.currentConfig) {
        // This is a simplified implementation - in reality, you'd need more sophisticated patching
        const keys = patch.field.split('.');
        let target: any = state.currentConfig.data;
        
        for (let i = 0; i < keys.length - 1; i++) {
          if (!target[keys[i]]) target[keys[i]] = {};
          target = target[keys[i]];
        }
        
        target[keys[keys.length - 1]] = patch.after;
        state.currentConfig.lastModified = patch.timestamp;
      }

      // Add to history and undo stack
      state.patchHistory.push(patch);
      state.undoStack.push(patch);
      state.redoStack = []; // Clear redo stack when new change is made
      state.isConfigModified = true;
    },
    undoPatch: (state) => {
      const patch = state.undoStack.pop();
      if (patch && state.currentConfig) {
        // Reverse the patch
        const keys = patch.field.split('.');
        let target: any = state.currentConfig.data;
        
        for (let i = 0; i < keys.length - 1; i++) {
          target = target[keys[i]];
        }
        
        target[keys[keys.length - 1]] = patch.before;
        
        state.redoStack.push(patch);
        state.currentConfig.lastModified = new Date().toISOString();
        state.isConfigModified = state.undoStack.length > 0;
      }
    },
    redoPatch: (state) => {
      const patch = state.redoStack.pop();
      if (patch && state.currentConfig) {
        // Reapply the patch
        const keys = patch.field.split('.');
        let target: any = state.currentConfig.data;
        
        for (let i = 0; i < keys.length - 1; i++) {
          target = target[keys[i]];
        }
        
        target[keys[keys.length - 1]] = patch.after;
        
        state.undoStack.push(patch);
        state.currentConfig.lastModified = new Date().toISOString();
        state.isConfigModified = true;
      }
    },
    setValidationErrors: (state, action: PayloadAction<ConfigState['validationErrors']>) => {
      state.validationErrors = action.payload;
    },
    loadFishRules: (state, action: PayloadAction<ConfigState['fishRules']>) => {
      state.fishRules = action.payload;
    },
    toggleFishRule: (state, action: PayloadAction<string>) => {
      const rule = state.fishRules.find(r => r.id === action.payload);
      if (rule) {
        rule.enabled = !rule.enabled;
      }
    },
    clearConfig: (state) => {
      state.currentConfig = null;
      state.patchHistory = [];
      state.undoStack = [];
      state.redoStack = [];
      state.isConfigModified = false;
      state.validationErrors = [];
    },
    markConfigSaved: (state) => {
      state.isConfigModified = false;
    },
  },
});

export const {
  loadConfig,
  applyPatch,
  undoPatch,
  redoPatch,
  setValidationErrors,
  loadFishRules,
  toggleFishRule,
  clearConfig,
  markConfigSaved,
} = configSlice.actions;

export default configSlice.reducer;
