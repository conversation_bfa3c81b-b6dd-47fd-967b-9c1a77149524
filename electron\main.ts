import { app, <PERSON><PERSON>erWindow, <PERSON>u, ipc<PERSON>ain, dialog, shell } from 'electron';
import { join } from 'path';
import { spawn } from 'child_process';
import Store from 'electron-store';
import * as fs from 'fs';
import * as os from 'os';

// Initialize logging
const logDir = join(os.homedir(), 'SecureBuilder', 'logs');
const logFile = join(logDir, `main-${new Date().toISOString().slice(0, 10)}.log`);

// Ensure log directory exists
try {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
} catch (error) {
  console.error('Failed to create log directory:', error);
}

// Logging function
function log(level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG', message: string, data?: any) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}${data ? ' ' + JSON.stringify(data) : ''}`;

  console.log(logMessage);

  try {
    fs.appendFileSync(logFile, logMessage + '\n');
  } catch (error) {
    console.error('Failed to write to log file:', error);
  }
}

log('INFO', 'SecureBuilder starting up...');

// Initialize electron store for persistent data
let store: Store;
try {
  store = new Store();
  log('INFO', 'Electron store initialized successfully');
} catch (error) {
  log('ERROR', 'Failed to initialize electron store', error);
  throw error;
}

const isDev = process.env.NODE_ENV === 'development';
log('INFO', `Running in ${isDev ? 'development' : 'production'} mode`);

let mainWindow: BrowserWindow | null = null;

function createWindow(): void {
  log('INFO', 'Creating main window...');

  try {
    // Create the browser window
    mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: join(__dirname, 'preload.js'),
      },
      icon: join(__dirname, '../assets/icon.png'), // We'll add this later
      show: false, // Don't show until ready
      titleBarStyle: 'default',
    });

    log('INFO', 'BrowserWindow created successfully');

    // Add error handlers
    mainWindow.webContents.on('crashed', () => {
      log('ERROR', 'Renderer process crashed');
    });

    mainWindow.webContents.on('unresponsive', () => {
      log('WARN', 'Renderer process became unresponsive');
    });

    mainWindow.webContents.on('responsive', () => {
      log('INFO', 'Renderer process became responsive again');
    });

    // Load the app
    if (isDev) {
      log('INFO', 'Loading development URL: http://localhost:5173');
      mainWindow.loadURL('http://localhost:5173').catch((error) => {
        log('ERROR', 'Failed to load development URL', error);
      });
      mainWindow.webContents.openDevTools();
    } else {
      const rendererPath = join(__dirname, '../renderer/index.html');
      log('INFO', 'Loading production file', { path: rendererPath });
      mainWindow.loadFile(rendererPath).catch((error) => {
        log('ERROR', 'Failed to load production file', error);
      });
    }

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
      log('INFO', 'Window ready to show');
      mainWindow?.show();
    });

    // Handle window closed
    mainWindow.on('closed', () => {
      log('INFO', 'Main window closed');
      mainWindow = null;
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      log('INFO', 'Opening external URL', { url });
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Log when page finishes loading
    mainWindow.webContents.once('did-finish-load', () => {
      log('INFO', 'Page finished loading');
    });

    // Log navigation events
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log('ERROR', 'Page failed to load', { errorCode, errorDescription, validatedURL });
    });

  } catch (error) {
    log('ERROR', 'Failed to create window', error);
    throw error;
  }
}

// Global error handlers
process.on('uncaughtException', (error) => {
  log('ERROR', 'Uncaught exception in main process', error);
});

process.on('unhandledRejection', (reason, promise) => {
  log('ERROR', 'Unhandled rejection in main process', { reason, promise });
});

// App event handlers
app.whenReady().then(() => {
  log('INFO', 'App is ready, creating window and menu...');
  try {
    createWindow();
    createMenu();
    log('INFO', 'Window and menu created successfully');
  } catch (error) {
    log('ERROR', 'Failed to create window or menu', error);
  }

  app.on('activate', () => {
    log('INFO', 'App activated');
    if (BrowserWindow.getAllWindows().length === 0) {
      log('INFO', 'No windows open, creating new window');
      createWindow();
    }
  });
}).catch((error) => {
  log('ERROR', 'App failed to become ready', error);
});

app.on('window-all-closed', () => {
  log('INFO', 'All windows closed');
  if (process.platform !== 'darwin') {
    log('INFO', 'Quitting app (not macOS)');
    app.quit();
  }
});

app.on('before-quit', () => {
  log('INFO', 'App is about to quit');
});

app.on('will-quit', () => {
  log('INFO', 'App will quit');
});

app.on('quit', () => {
  log('INFO', 'App quit');
});

// Create application menu
function createMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Project',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow?.webContents.send('menu-new-project');
          },
        },
        {
          label: 'Open Project',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow?.webContents.send('menu-open-project');
          },
        },
        {
          label: 'Save Project',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow?.webContents.send('menu-save-project');
          },
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: 'Build',
      submenu: [
        {
          label: 'Build Executable',
          accelerator: 'F5',
          click: () => {
            mainWindow?.webContents.send('menu-build-exe');
          },
        },
        {
          label: 'Clean Build',
          click: () => {
            mainWindow?.webContents.send('menu-clean-build');
          },
        },
      ],
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
      ],
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About SecureBuilder',
          click: () => {
            mainWindow?.webContents.send('menu-about');
          },
        },
        {
          label: 'Documentation',
          click: () => {
            shell.openExternal('https://github.com/securebuilder/docs');
          },
        },
      ],
    },
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers
ipcMain.handle('store-get', (_, key: string) => {
  try {
    log('DEBUG', 'Getting store value', { key });
    const value = store.get(key);
    log('DEBUG', 'Store value retrieved', { key, hasValue: value !== undefined });
    return value;
  } catch (error) {
    log('ERROR', 'Failed to get store value', { key, error });
    throw error;
  }
});

ipcMain.handle('store-set', (_, key: string, value: any) => {
  try {
    log('DEBUG', 'Setting store value', { key, valueType: typeof value });
    store.set(key, value);
    log('DEBUG', 'Store value set successfully', { key });
  } catch (error) {
    log('ERROR', 'Failed to set store value', { key, error });
    throw error;
  }
});

ipcMain.handle('store-delete', (_, key: string) => {
  try {
    log('DEBUG', 'Deleting store value', { key });
    store.delete(key);
    log('DEBUG', 'Store value deleted successfully', { key });
  } catch (error) {
    log('ERROR', 'Failed to delete store value', { key, error });
    throw error;
  }
});

ipcMain.handle('show-open-dialog', async (_, options) => {
  try {
    log('DEBUG', 'Showing open dialog', { options });
    const result = await dialog.showOpenDialog(mainWindow!, options);
    log('DEBUG', 'Open dialog result', { canceled: result.canceled, fileCount: result.filePaths.length });
    return result;
  } catch (error) {
    log('ERROR', 'Failed to show open dialog', { error });
    throw error;
  }
});

ipcMain.handle('show-save-dialog', async (_, options) => {
  try {
    log('DEBUG', 'Showing save dialog', { options });
    const result = await dialog.showSaveDialog(mainWindow!, options);
    log('DEBUG', 'Save dialog result', { canceled: result.canceled, hasFilePath: !!result.filePath });
    return result;
  } catch (error) {
    log('ERROR', 'Failed to show save dialog', { error });
    throw error;
  }
});

ipcMain.handle('execute-command', async (_, command: string, args: string[], cwd?: string) => {
  return new Promise((resolve, reject) => {
    try {
      log('INFO', 'Executing command', { command, args, cwd });

      const process = spawn(command, args, {
        cwd: cwd || undefined,
        shell: true
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        log('DEBUG', 'Command stdout', { command, output: output.slice(0, 100) });
        // Send real-time output to renderer
        mainWindow?.webContents.send('command-output', output);
      });

      process.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        log('DEBUG', 'Command stderr', { command, output: output.slice(0, 100) });
        mainWindow?.webContents.send('command-error', output);
      });

      process.on('close', (code) => {
        log('INFO', 'Command completed', { command, code, stdoutLength: stdout.length, stderrLength: stderr.length });
        resolve({ code, stdout, stderr });
      });

      process.on('error', (error) => {
        log('ERROR', 'Command failed', { command, error });
        reject(error);
      });
    } catch (error) {
      log('ERROR', 'Failed to spawn command', { command, error });
      reject(error);
    }
  });
});
