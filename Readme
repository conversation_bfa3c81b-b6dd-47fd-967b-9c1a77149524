# SecureBuilder – Technical Overview (v3 Phase 1)

> **Mission‑critical rule:** *one self‑contained Python script* drives the entire application; no satellite modules.

---

## ✅ CURRENTLY IMPLEMENTED (Phase 1)

*(all items live in `SecureBuilder_v3_Phase1.py`)*

### 1 · GUI LAYOUT & UX

\*Framework \*: **CustomTkinter**

* Single‑window grid layout, dark theme by default
* “Browse” buttons for **Script**, **Output Folder**, plus new left‑column
  browse buttons:

  * **ZIP Bundle Location** (optional)
  * **MSI Location** (optional)
  * **Wrapped EXE Location** (optional)
* Right‑side **Browse Output Folder** shortcut
* Dev‑mode toggle greys out advanced widgets when off

### 2 · BUILD ENGINE

* **PyInstaller** → `--onefile --noconfirm`
* Modes: **Clean**, **Protected**, **Both** (select via *Build Mode*)
* Optional **MSI** packaging (msi‑packager CLI)
* Optional **ZIP** bundling (collects EXE + MSI + README)
* Unified **README.txt** auto‑generated in every output & backup folder
* Standard **splash.png** and **icon.ico** supported; placeholder until global branding asset is finalised

### 3 · UNLOCK‑WRAPPER SYSTEM (Protected Builds)

| Stage       | File / Action                                              |
| ----------- | ---------------------------------------------------------- |
| Runtime     | EXE shows validation code (machine hash + salt)            |
| User  ➜ Dev | Copy validation code to developer                          |
| Dev tool    | `generate_unlock_<App>.py` (auto‑built) returns unlock key |
| Runtime     | User enters key → stored in `unlock.token`                 |

### 4 · PROFILE & CONFIG FILES

| File                             | Purpose                                                     |
| -------------------------------- | ----------------------------------------------------------- |
| **`<App>.sbproj`**               | Saves every GUI field inc. new browse paths, Dev Mode, etc. |
| **`app_config.json`** (Phase 2)  | Global defaults (theme, logging)                            |
| **`user_config.json`** (Phase 2) | Per‑user MRU folders, last profile                          |

### 5 · OUTPUT TREE & BACKUPS

```
~/SecureBuilder_Backups/
└─ MyApp_2025‑06‑10_14‑32/
   ├─ dist/               # PyInstaller output
   │   └─ MyApp.exe
   ├─ dist_msi/           # Optional MSI
   ├─ unlock/             # Dev unlock generator + README
   ├─ temp/               # Wrapped .py + build logs
   └─ README.txt          # Build metadata & instructions
```

*Rules* : timestamped sub‑dir per build, tidy retention; older backups pruned by user or future auto‑clean.

### 6 · WIZARD MODE

| Level                 | Behaviour                                                                              |
| --------------------- | -------------------------------------------------------------------------------------- |
| **Apprentice** 🪄     | Generates digital clock script → auto‑fills & opens Explorer; enlarged console window. |
| **Level 3 Wizard** 🎩 | *UI ready*; Phase 2 will pre‑fill splash/icon & enable ZIP.                            |
| **Epic Mage** 🌌      | Placeholder for PyArmor, CLI export, script analyser (Phase 3+).                       |

### 7 · DRAG‑AND‑DROP (“Drop Mode”)

Phase 2 feature flag — drop zone accepts `.py`, `.ico`, `.png`, `.sbproj`, `.zip`; auto‑fills matching fields; can be default or toggle.

### 8 · DEV MODE DETAILS

* Toggles advanced widgets; disabled controls are grey‑out + tooltip “Dev Mode only”.
* No pop‑ups; status bar hint shows when a field is locked.

### 9 · ERROR & SUCCESS UX

* All missing‑input checks handled with calm `messagebox.showwarning`.
* Post‑build toast summarises clean / protected / MSI / ZIP success.

---

## 🔮 ROADMAP

### Phase 2 (in progress)

1. **Level 3 Wizard logic** (prefill splash/icon/ZIP, Dev Mode on)
2. **Config files** `app_config.json` & `user_config.json` (load + fallback + backup)
3. **README.txt injector** for user + dev backups
4. **Drag‑and‑Drop panel** implementation

### Phase 3

* **Epic Mage**: PyArmor encryption, CLI script export, hidden‑import analyser
* Launch‑sound picker & validation
* Dev Mode log pane (live build log)

### Phase 4

* Auto‑update checker, theming presets, branded splash rollout
* Output tree auto‑clean utility

---

## 🧠 PRINCIPLES (Gold Standard Recap)

* Single‑file app, modular internal classes/functions
* Clear separation of user vs. dev artefacts
* Config + backup hygiene
* Dev‑Mode gates power features without overwhelming novices
* Wizard & Drop‑Mode for button‑averse users
* All heavy ops (PyInstaller, msi‑packager) run in worker threads in future for UI responsiveness

SecureBuilder is evolving toward a one‑click, full‑lifecycle Python → protected‑EXE studio, with optional advanced layers for security‑minded developers.




1. 🤔 The build_mode vs. UI mismatch
In test_2.py

python
Copy
Edit
self.build_mode = tk.StringVar(value="Clean Only")
…  
def build_exe(self):
    build_mode = self.build_mode.get()
    …
    if build_mode in ("Clean Only", "Both"):
        …
    if protect and build_mode in ("Wrapped Only", "Both"):
        …
Problem: There is no OptionMenu or other UI widget bound to self.build_mode. It never changes from its default (“Clean Only”), and if you try to set it programmatically, the user has no way to pick “Wrapped Only” or “Both.”

2. 🛠️ Missing “protected_output_mode” control
test_2.py defines self.protected_output_mode = tk.StringVar("Secure Only") and even reads it in build_exe, but the UI never shows an OptionMenu for “Secure Only / User Folder / Both.”

3. 🔎 Unwired “browse” paths
You added zip_location, msi_location, wrapped_location StringVars in the latest UI file, but

In test_2.py they don’t exist (so build logic reading them will crash)

Even in the Phase-5 UI update, the build logic never uses those variables—defaults always go to the main output folder.

4. 🗄️ Splash/Icon/Sound fields
test_2.py declares splash_path, icon_path, sound_path, but doesn’t put any entry or “Browse” button in create_widgets.

Yet build_exe() tries to append --splash= and --icon= flags based on them, so those flags never get set.

5. 🧰 Profile persistence for new fields
save_profile() / load_profile() in test_2.py only handle the old fields. We need to extend them to also persist:

Copy
Edit
zip_location, msi_location, wrapped_location,
splash_path, icon_path, sound_path,
build_mode, protected_output_mode
6. ✅ What is there (good!)
Core build_exe() structure (PyInstaller invocation, wrap logic, ZIP + MSI collectors)

Unlock-wrapper injection and dev-unlock tool code

Wizard scaffolding and Apprentice clock wizard

Basic browse for script + output folder

🚀 Refactoring Plan — SecureBuilder_v3_Phase1.py
Add UI controls in create_widgets():

OptionMenu for Build Mode (Clean Only, Wrapped Only, Both)

OptionMenu for Protected EXE Output (Secure Only, User Folder, Both)

Entry + Browse buttons for splash_path, icon_path, sound_path

Entry + Browse buttons for zip_location, msi_location, wrapped_location

(Keep script_path & output_folder browsers)

Wire all these variables into build_exe():

If zip_location set, zip into that instead of main output

If msi_location set, place MSI there

If wrapped_location set, place wrapped EXE there

Fallbacks when paths are blank

Extend save_profile() / load_profile() to include every new variable so configs fully restore the UI state.

Fix Apprentice clock wizard:

Use raw string for clock_code so ANSI escapes don’t break

Launch Explorer with the new file selected

Spawn a larger terminal window (PowerShell) for visibility

Test & error‐handle:

Warn if build_mode or protected_output_mode is ever blank/invalid

Ensure no uncaught exceptions for missing fields