import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  Save as SaveIcon,
  Restore as RestoreIcon,
  Delete as DeleteIcon,
  Folder as FolderIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { setTheme, addNotification } from '@/store/slices/uiSlice';

interface AppSettings {
  theme: 'light' | 'dark';
  autoSave: boolean;
  buildNotifications: boolean;
  defaultOutputPath: string;
  pyinstallerPath: string;
  pyarmorPath: string;
  developerName: string;
  enableLogging: boolean;
  logLevel: 'error' | 'warning' | 'info' | 'debug';
  maxRecentProjects: number;
  enableTelemetry: boolean;
}

const Settings: React.FC = () => {
  const dispatch = useDispatch();
  const { theme } = useSelector((state: RootState) => state.ui);
  
  const [settings, setSettings] = useState<AppSettings>({
    theme: theme,
    autoSave: true,
    buildNotifications: true,
    defaultOutputPath: '',
    pyinstallerPath: 'pyinstaller',
    pyarmorPath: 'pyarmor',
    developerName: 'Developer',
    enableLogging: true,
    logLevel: 'info',
    maxRecentProjects: 10,
    enableTelemetry: false,
  });

  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const handleSettingChange = (key: keyof AppSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setUnsavedChanges(true);
    
    // Apply theme change immediately
    if (key === 'theme') {
      dispatch(setTheme(value));
    }
  };

  const handleSaveSettings = async () => {
    try {
      if (window.electronAPI) {
        await window.electronAPI.store.set('appSettings', settings);
      }
      
      setUnsavedChanges(false);
      dispatch(addNotification({
        type: 'success',
        title: 'Settings Saved',
        message: 'Your settings have been saved successfully.',
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save settings.',
      }));
    }
  };

  const handleResetSettings = () => {
    const defaultSettings: AppSettings = {
      theme: 'dark',
      autoSave: true,
      buildNotifications: true,
      defaultOutputPath: '',
      pyinstallerPath: 'pyinstaller',
      pyarmorPath: 'pyarmor',
      developerName: 'Developer',
      enableLogging: true,
      logLevel: 'info',
      maxRecentProjects: 10,
      enableTelemetry: false,
    };
    
    setSettings(defaultSettings);
    setUnsavedChanges(true);
    dispatch(setTheme(defaultSettings.theme));
    
    dispatch(addNotification({
      type: 'info',
      title: 'Settings Reset',
      message: 'Settings have been reset to defaults.',
    }));
  };

  const handleBrowsePath = async (settingKey: 'defaultOutputPath' | 'pyinstallerPath' | 'pyarmorPath') => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: settingKey === 'defaultOutputPath' ? ['openDirectory'] : ['openFile'],
          filters: settingKey === 'defaultOutputPath' ? [] : [
            { name: 'Executable Files', extensions: ['exe'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          handleSettingChange(settingKey, result.filePaths[0]);
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Browse Failed',
          message: 'Failed to browse for path.',
        }));
      }
    }
  };

  const clearAppData = async () => {
    try {
      if (window.electronAPI) {
        await window.electronAPI.store.delete('recentProjects');
        await window.electronAPI.store.delete('buildHistory');
      }
      
      dispatch(addNotification({
        type: 'success',
        title: 'Data Cleared',
        message: 'Application data has been cleared.',
      }));
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Clear Failed',
        message: 'Failed to clear application data.',
      }));
    }
  };

  // Load settings on component mount
  React.useEffect(() => {
    const loadSettings = async () => {
      if (window.electronAPI) {
        try {
          const savedSettings = await window.electronAPI.store.get('appSettings');
          if (savedSettings) {
            setSettings(savedSettings);
            dispatch(setTheme(savedSettings.theme));
          }
        } catch (error) {
          console.error('Failed to load settings:', error);
        }
      }
    };
    
    loadSettings();
  }, [dispatch]);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Settings
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RestoreIcon />}
            onClick={handleResetSettings}
          >
            Reset to Defaults
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSaveSettings}
            disabled={!unsavedChanges}
          >
            Save Settings
          </Button>
        </Box>
      </Box>

      {unsavedChanges && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          You have unsaved changes. Don't forget to save your settings.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                General
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={settings.theme}
                    label="Theme"
                    onChange={(e) => handleSettingChange('theme', e.target.value)}
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Developer Name"
                  value={settings.developerName}
                  onChange={(e) => handleSettingChange('developerName', e.target.value)}
                  helperText="Used in patch signatures and build metadata"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.autoSave}
                      onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                    />
                  }
                  label="Auto-save projects"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.buildNotifications}
                      onChange={(e) => handleSettingChange('buildNotifications', e.target.checked)}
                    />
                  }
                  label="Show build notifications"
                />
                
                <TextField
                  fullWidth
                  type="number"
                  label="Max Recent Projects"
                  value={settings.maxRecentProjects}
                  onChange={(e) => handleSettingChange('maxRecentProjects', parseInt(e.target.value))}
                  inputProps={{ min: 1, max: 50 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Build Tools */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Build Tools
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    label="Default Output Path"
                    value={settings.defaultOutputPath}
                    onChange={(e) => handleSettingChange('defaultOutputPath', e.target.value)}
                    placeholder="Leave empty for project directory"
                  />
                  <Button
                    variant="outlined"
                    startIcon={<FolderIcon />}
                    onClick={() => handleBrowsePath('defaultOutputPath')}
                  >
                    Browse
                  </Button>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    label="PyInstaller Path"
                    value={settings.pyinstallerPath}
                    onChange={(e) => handleSettingChange('pyinstallerPath', e.target.value)}
                    placeholder="pyinstaller"
                  />
                  <Button
                    variant="outlined"
                    startIcon={<FolderIcon />}
                    onClick={() => handleBrowsePath('pyinstallerPath')}
                  >
                    Browse
                  </Button>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    label="PyArmor Path"
                    value={settings.pyarmorPath}
                    onChange={(e) => handleSettingChange('pyarmorPath', e.target.value)}
                    placeholder="pyarmor"
                  />
                  <Button
                    variant="outlined"
                    startIcon={<FolderIcon />}
                    onClick={() => handleBrowsePath('pyarmorPath')}
                  >
                    Browse
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Logging & Debug */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Logging & Debug
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableLogging}
                      onChange={(e) => handleSettingChange('enableLogging', e.target.checked)}
                    />
                  }
                  label="Enable logging"
                />
                
                <FormControl fullWidth disabled={!settings.enableLogging}>
                  <InputLabel>Log Level</InputLabel>
                  <Select
                    value={settings.logLevel}
                    label="Log Level"
                    onChange={(e) => handleSettingChange('logLevel', e.target.value)}
                  >
                    <MenuItem value="error">Error</MenuItem>
                    <MenuItem value="warning">Warning</MenuItem>
                    <MenuItem value="info">Info</MenuItem>
                    <MenuItem value="debug">Debug</MenuItem>
                  </Select>
                </FormControl>
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableTelemetry}
                      onChange={(e) => handleSettingChange('enableTelemetry', e.target.checked)}
                    />
                  }
                  label="Enable anonymous telemetry"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Data Management */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Data Management
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Clear Recent Projects"
                    secondary="Remove all recent project history"
                  />
                  <ListItemSecondaryAction>
                    <IconButton edge="end" onClick={clearAppData}>
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Clear Build History"
                    secondary="Remove all build history and logs"
                  />
                  <ListItemSecondaryAction>
                    <IconButton edge="end" onClick={clearAppData}>
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
              
              <Divider sx={{ my: 2 }} />
              
              <Alert severity="info" icon={<InfoIcon />}>
                <Typography variant="body2">
                  <strong>SecureBuilder v3.0</strong><br/>
                  Modern Python executable builder with economic configuration management.
                  Built with React, Electron, and TypeScript.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;
