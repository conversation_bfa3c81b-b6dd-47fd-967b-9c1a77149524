@echo off
echo.
echo ========================================
echo   SecureBuilder v3.0 - Quick Start
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available
    echo Please reinstall Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo [INFO] Node.js and npm are available
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    echo This may take a few minutes on first run...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
    echo.
    echo [SUCCESS] Dependencies installed successfully!
    echo.
) else (
    echo [INFO] Dependencies already installed
    echo.
)

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Python is not installed or not in PATH
    echo SecureBuilder needs Python for building executables
    echo Please install Python from https://python.org/
    echo.
)

REM Check if PyInstaller is available
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] PyInstaller is not installed
    echo Run: pip install pyinstaller
    echo.
)

echo [INFO] Starting SecureBuilder v3.0...
echo.
echo The application will open in a new window
echo Press Ctrl+C to stop the development server
echo.

REM Start the development server
npm run dev

echo.
echo [INFO] SecureBuilder v3.0 has been stopped
pause
