import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { Provider } from 'react-redux';
import { store } from '@/store/store';
import Layout from '@/components/Layout/Layout';
import Dashboard from '@/pages/Dashboard/Dashboard';
import ProjectBuilder from '@/pages/ProjectBuilder/ProjectBuilder';
import ConfigManager from '@/pages/ConfigManager/ConfigManager';
import Settings from '@/pages/Settings/Settings';
import { logger } from '@/utils/logger';

// Create dark theme
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
    },
    secondary: {
      main: '#f48fb1',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
  },
});

function App() {
  React.useEffect(() => {
    logger.info('App component mounted', {
      userAgent: navigator.userAgent,
      platform: window.electronAPI?.platform || 'unknown',
      version: window.electronAPI?.version || 'unknown'
    }, 'App');

    // Check if Electron API is available
    if (window.electronAPI) {
      logger.info('Electron API is available', {
        hasStore: !!window.electronAPI.store,
        hasDialog: !!window.electronAPI.dialog,
        hasExecuteCommand: !!window.electronAPI.executeCommand,
      }, 'App');
    } else {
      logger.warn('Electron API is not available - running in browser mode?', {}, 'App');
    }

    return () => {
      logger.info('App component unmounting', {}, 'App');
    };
  }, []);

  try {
    logger.debug('Rendering App component', {}, 'App');

    return (
      <Provider store={store}>
        <ThemeProvider theme={darkTheme}>
          <CssBaseline />
          <Router>
            <Box sx={{ display: 'flex', height: '100vh' }}>
              <Layout>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/builder" element={<ProjectBuilder />} />
                  <Route path="/config" element={<ConfigManager />} />
                  <Route path="/settings" element={<Settings />} />
                </Routes>
              </Layout>
            </Box>
          </Router>
        </ThemeProvider>
      </Provider>
    );
  } catch (error) {
    logger.error('Error rendering App component', error, 'App');
    throw error;
  }
}

export default App;
