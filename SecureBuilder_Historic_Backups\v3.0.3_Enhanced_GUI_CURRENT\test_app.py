#!/usr/bin/env python3
"""
Simple Test Application for SecureBuilder
==========================================
This is a test application to verify that SecureBuilder can successfully
create executables from Python scripts.
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
from datetime import datetime

class TestApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SecureBuilder Test App")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Create UI
        self.create_widgets()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """Create the application widgets"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🛡️ SecureBuilder Test App",
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=20)
        
        # Info text
        info_text = tk.Text(
            self.root,
            height=8,
            width=45,
            wrap=tk.WORD,
            font=("Arial", 10)
        )
        info_text.pack(pady=10, padx=20)
        
        # Add some test information
        test_info = f"""✅ SUCCESS! This executable was built by SecureBuilder v3.0

📊 Build Information:
• Built on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Python Version: {sys.version.split()[0]}
• Platform: {sys.platform}
• Executable Path: {sys.executable}

🎯 Test Results:
• GUI Framework: Tkinter ✅
• File System Access: {self.test_file_access()}
• Module Imports: {self.test_imports()}

This confirms that SecureBuilder successfully created a working executable!"""
        
        info_text.insert("1.0", test_info)
        info_text.config(state=tk.DISABLED)
        
        # Buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # Test button
        test_btn = tk.Button(
            button_frame,
            text="🧪 Run Tests",
            command=self.run_tests,
            font=("Arial", 12),
            bg="lightgreen",
            width=12
        )
        test_btn.pack(side=tk.LEFT, padx=5)
        
        # About button
        about_btn = tk.Button(
            button_frame,
            text="ℹ️ About",
            command=self.show_about,
            font=("Arial", 12),
            bg="lightblue",
            width=12
        )
        about_btn.pack(side=tk.LEFT, padx=5)
        
        # Exit button
        exit_btn = tk.Button(
            button_frame,
            text="❌ Exit",
            command=self.root.quit,
            font=("Arial", 12),
            bg="lightcoral",
            width=12
        )
        exit_btn.pack(side=tk.LEFT, padx=5)
        
    def test_file_access(self):
        """Test file system access"""
        try:
            test_file = "test_write.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return "✅ Working"
        except:
            return "❌ Failed"
    
    def test_imports(self):
        """Test common module imports"""
        try:
            import json
            import datetime
            import os
            import sys
            return "✅ Working"
        except:
            return "❌ Failed"
    
    def run_tests(self):
        """Run comprehensive tests"""
        results = []
        
        # Test 1: Basic functionality
        results.append("🧪 Running SecureBuilder Tests...")
        results.append("")
        
        # Test 2: File operations
        try:
            with open("test.txt", "w") as f:
                f.write("SecureBuilder test file")
            os.remove("test.txt")
            results.append("✅ File Operations: PASSED")
        except Exception as e:
            results.append(f"❌ File Operations: FAILED - {e}")
        
        # Test 3: GUI functionality
        try:
            test_window = tk.Toplevel(self.root)
            test_window.title("Test Window")
            test_window.geometry("200x100")
            tk.Label(test_window, text="Test successful!").pack(pady=20)
            test_window.after(2000, test_window.destroy)
            results.append("✅ GUI Operations: PASSED")
        except Exception as e:
            results.append(f"❌ GUI Operations: FAILED - {e}")
        
        # Test 4: System information
        try:
            import platform
            results.append(f"✅ System Info: {platform.system()} {platform.release()}")
        except Exception as e:
            results.append(f"❌ System Info: FAILED - {e}")
        
        # Show results
        messagebox.showinfo("Test Results", "\n".join(results))
    
    def show_about(self):
        """Show about dialog"""
        about_text = """🛡️ SecureBuilder Test Application

This application was created to test the SecureBuilder v3.0 
executable building process.

Features tested:
• GUI creation with Tkinter
• File system operations
• Module imports
• System information access
• Error handling

If you can see this dialog, SecureBuilder successfully 
created a working executable!

Created by: SecureBuilder v3.0 Professional
Build Date: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        messagebox.showinfo("About SecureBuilder Test App", about_text)
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main application entry point"""
    print("🛡️ SecureBuilder Test App Starting...")
    print(f"Python Version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Executable: {sys.executable}")
    print("=" * 50)
    
    try:
        app = TestApp()
        app.run()
        print("✅ Application closed successfully")
    except Exception as e:
        print(f"❌ Error running application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
