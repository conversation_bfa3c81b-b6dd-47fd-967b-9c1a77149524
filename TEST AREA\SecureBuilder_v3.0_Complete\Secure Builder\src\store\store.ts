import { configureStore } from '@reduxjs/toolkit';
import projectReducer from './slices/projectSlice';
import buildReducer from './slices/buildSlice';
import configReducer from './slices/configSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    project: projectReducer,
    build: buildReducer,
    config: configReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
