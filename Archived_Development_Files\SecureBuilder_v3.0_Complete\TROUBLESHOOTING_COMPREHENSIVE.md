# 🔧 SecureBuilder v3.0 - Comprehensive Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. Dependencies & Installation Issues

#### Issue: "yarn install" fails or takes too long
**Symptoms:**
- Installation hangs or fails
- Network timeout errors
- Disk space errors

**Solutions:**
```powershell
# Clear yarn cache
yarn cache clean

# Use different registry
yarn install --registry https://registry.npmjs.org/

# Install with verbose logging
yarn install --verbose

# Skip optional dependencies
yarn install --ignore-optional
```

#### Issue: "node_modules" corruption
**Symptoms:**
- Random module not found errors
- Inconsistent behavior

**Solutions:**
```powershell
# Complete reset
rmdir /s node_modules
del yarn.lock
yarn install
```

### 2. Development Server Issues

#### Issue: Vite server won't start
**Symptoms:**
- "Cannot find module 'vite'" errors
- Port already in use errors

**Solutions:**
```powershell
# Check if port is in use
netstat -ano | findstr :5173

# Kill process using port
taskkill /PID <PID> /F

# Start with different port
node ".\node_modules\vite\bin\vite.js" --port 5174

# Check vite installation
dir node_modules\vite\bin\vite.js
```

#### Issue: Hot reload not working
**Symptoms:**
- Changes don't appear automatically
- Manual refresh required

**Solutions:**
```powershell
# Check file watching limits (Linux/macOS)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf

# Restart with polling (Windows)
node ".\node_modules\vite\bin\vite.js" --force
```

### 3. Electron Issues

#### Issue: Electron window doesn't open
**Symptoms:**
- Process starts but no window appears
- Electron crashes immediately

**Solutions:**
```powershell
# Check environment variable
echo $env:NODE_ENV

# Set development mode explicitly
$env:NODE_ENV="development"

# Run with debug output
$env:ELECTRON_ENABLE_LOGGING="true"
node ".\node_modules\electron\cli.js" .

# Check Electron installation
dir node_modules\electron\dist\electron.exe
```

#### Issue: Electron shows blank/error page
**Symptoms:**
- White screen in Electron window
- "Failed to load URL" errors

**Solutions:**
1. **Ensure Vite is running first**
2. **Check URL in Electron DevTools:**
   - Press Ctrl+Shift+I in Electron
   - Check Console for errors
   - Verify URL is http://localhost:5173

3. **Manual URL check:**
   ```javascript
   // In Electron DevTools Console
   location.href = 'http://localhost:5173'
   ```

### 4. Path & File Resolution Issues

#### Issue: "Cannot find module" errors
**Symptoms:**
- Import errors for React, Material-UI, etc.
- Path resolution failures

**Solutions:**
```powershell
# Check for conflicting configs
dir ..\..\..\vite.config.ts
dir ..\..\..\package.json

# Rename conflicting files
ren "..\..\..\vite.config.ts" "..\..\..\vite.config.ts.backup"
ren "..\..\..\src" "..\..\..\src.backup"

# Verify project structure
dir src\
dir electron\
dir node_modules\
```

#### Issue: TypeScript compilation errors
**Symptoms:**
- .ts files not compiling
- Type errors in console

**Solutions:**
```powershell
# Check TypeScript installation
dir node_modules\typescript\bin\tsc.js

# Manual TypeScript build
node ".\node_modules\typescript\bin\tsc.js" -p tsconfig.electron.json

# Check tsconfig files
type tsconfig.json
type tsconfig.electron.json
```

### 5. Performance Issues

#### Issue: Slow startup or high memory usage
**Symptoms:**
- Long loading times
- System becomes unresponsive

**Solutions:**
```powershell
# Check system resources
tasklist | findstr node
tasklist | findstr electron

# Reduce memory usage
$env:NODE_OPTIONS="--max-old-space-size=4096"

# Disable source maps in development
# Edit vite.config.ts: build: { sourcemap: false }
```

### 6. Network & Firewall Issues

#### Issue: Cannot access http://localhost:5173
**Symptoms:**
- Browser shows "connection refused"
- Timeout errors

**Solutions:**
```powershell
# Check if Vite is actually running
netstat -ano | findstr :5173

# Test with curl (if available)
curl http://localhost:5173

# Try different localhost variants
# http://127.0.0.1:5173
# http://[::1]:5173

# Check Windows Firewall
# Windows Security > Firewall > Allow an app
```

### 7. Environment-Specific Issues

#### Windows-Specific Issues

**PowerShell Execution Policy:**
```powershell
# Check current policy
Get-ExecutionPolicy

# Set policy for current user
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Long Path Support:**
```powershell
# Enable long paths in Windows
# Computer Configuration > Administrative Templates > System > Filesystem
# Enable "Enable Win32 long paths"
```

#### macOS-Specific Issues

**Permission Issues:**
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules
```

#### Linux-Specific Issues

**File Watching Limits:**
```bash
# Increase inotify limits
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 🔍 Diagnostic Commands

### System Information
```powershell
# Node.js version
node --version

# Yarn version
yarn --version

# Python version (for PyInstaller)
python --version

# System info
systeminfo | findstr "OS Name"
systeminfo | findstr "Total Physical Memory"
```

### Project Health Check
```powershell
# Verify project structure
dir
dir src\
dir electron\
dir node_modules\.bin\

# Check package.json
type package.json | findstr "name\|version\|scripts"

# Verify dependencies
yarn list --depth=0
```

### Process Monitoring
```powershell
# Check running Node processes
tasklist | findstr node

# Check port usage
netstat -ano | findstr :5173
netstat -ano | findstr :5174

# Monitor resource usage
wmic process where name="node.exe" get ProcessId,PageFileUsage,WorkingSetSize
```

## 🚑 Emergency Recovery Procedures

### Level 1: Soft Reset
```powershell
# Stop all processes (Ctrl+C in terminals)
# Refresh browser (F5)
# Restart development servers
```

### Level 2: Process Reset
```powershell
# Kill all Node processes
taskkill /IM node.exe /F
taskkill /IM electron.exe /F

# Clear temporary files
del /q %TEMP%\vite-*
del /q %TEMP%\electron-*

# Restart development environment
```

### Level 3: Dependency Reset
```powershell
# Stop all processes
# Remove node_modules
rmdir /s node_modules

# Clear caches
yarn cache clean
npm cache clean --force

# Reinstall
yarn install
```

### Level 4: Complete Reset
```powershell
# Backup your changes first!
# Remove all generated files
rmdir /s node_modules
rmdir /s dist
del yarn.lock
del package-lock.json

# Restore from backup
# Extract SecureBuilder_v3.0_Complete.zip
# Follow setup instructions
```

## 📊 Performance Optimization

### Development Mode Optimizations
```powershell
# Disable source maps for faster builds
# Edit vite.config.ts:
# build: { sourcemap: false }

# Reduce bundle analysis
# Comment out bundle analyzer plugins

# Limit concurrent processes
$env:UV_THREADPOOL_SIZE="4"
```

### Memory Management
```powershell
# Increase Node.js memory limit
$env:NODE_OPTIONS="--max-old-space-size=8192"

# Monitor memory usage
wmic process where name="node.exe" get WorkingSetSize
```

## 📞 Getting Additional Help

### Log Files to Check
- **Vite logs**: Terminal output where Vite is running
- **Electron logs**: `%USERPROFILE%\AppData\Roaming\SecureBuilder\logs\`
- **System logs**: Windows Event Viewer > Application logs

### Information to Gather
1. **Exact error messages** (copy full text)
2. **System specifications** (OS, RAM, Node version)
3. **Steps to reproduce** the issue
4. **Terminal output** from both Vite and Electron
5. **Browser console errors** (F12 > Console)

### Debug Mode
```powershell
# Enable verbose logging
$env:DEBUG="*"
$env:ELECTRON_ENABLE_LOGGING="true"
$env:NODE_ENV="development"

# Run with debug output
node ".\node_modules\vite\bin\vite.js" --debug
```

---

**💡 Remember: Most issues can be resolved by following the solutions in order from Level 1 to Level 4!**
