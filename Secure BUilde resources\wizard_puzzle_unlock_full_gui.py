
import customtkinter as ctk, random

CODE = [True, True, False, True]

class WizardPuzzle(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Wizard Puzzle Unlock")
        self.geometry("300x220")
        self.vars = []
        for i in range(4):
            v = ctk.BooleanVar(value=False)
            self.vars.append(v)
            ctk.CTkCheckBox(self, text=f"Rune {i+1}", variable=v).pack(anchor="w", padx=40, pady=4)
        ctk.CTkButton(self, text="Unlock!", command=self.try_unlock).pack(pady=12)
        self.msg = ctk.CTkLabel(self, text="")
        self.msg.pack()

    def try_unlock(self):
        attempt = [v.get() for v in self.vars]
        if attempt == CODE:
            self.msg.configure(text="🔓 UNLOCKED", text_color="springgreen")
        else:
            self.msg.configure(text=random.choice(("Nope", "Try again", "Wrong combo")), text_color="firebrick")

if __name__ == "__main__":
    WizardPuzzle().mainloop()
