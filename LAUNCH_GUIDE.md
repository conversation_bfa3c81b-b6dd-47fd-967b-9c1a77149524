# 🚀 SecureBuilder v3.0 - Launch Guide

## 📦 Package Contents

Your `SecureBuilder_v3.0_Complete.zip` contains a complete modern React + Electron application with:

- **Modern Architecture**: React 18 + TypeScript + Electron 28
- **Professional UI**: Material-UI with dark theme
- **State Management**: Redux Toolkit
- **Build System**: Vite + TypeScript compilation
- **Cross-Platform**: Windows, macOS, Linux support

## 🔧 Prerequisites

Before launching SecureBuilder v3.0, ensure you have:

### Required Software
1. **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/)
2. **Python 3.8+** - [Download from python.org](https://python.org/)
3. **Git** (optional) - [Download from git-scm.com](https://git-scm.com/)

### Python Dependencies
```bash
pip install pyinstaller pyarmor
```

## 📁 Installation Steps

### Step 1: Extract the ZIP
1. Extract `SecureBuilder_v3.0_Complete.zip` to your desired location
2. Navigate to the extracted folder in your terminal/command prompt

### Step 2: Install Dependencies
```bash
# Install Node.js dependencies
npm install
```

### Step 3: Verify Installation
```bash
# Check if everything is installed correctly
npm run lint
```

## 🎯 Launch Methods

### Method 1: Development Mode (Recommended for Testing)
```bash
# Start the development server with hot reload
npm run dev
```
This will:
- Start React dev server on `http://localhost:5173`
- Launch Electron with DevTools enabled
- Enable hot reload for instant code changes

### Method 2: Production Build
```bash
# Build the application
npm run build

# Package for your platform
npm run package:win    # Windows
npm run package:mac    # macOS  
npm run package:linux  # Linux
```

### Method 3: Quick Preview
```bash
# Build and preview
npm run build
npm run preview
```

## 🖥️ Platform-Specific Instructions

### Windows
```cmd
# Open Command Prompt or PowerShell in the project folder
npm install
npm run dev
```

### macOS
```bash
# Open Terminal in the project folder
npm install
npm run dev
```

### Linux
```bash
# Open terminal in the project folder
npm install
npm run dev
```

## 🔍 Troubleshooting

### Common Issues

#### 1. "npm not found"
**Solution**: Install Node.js from [nodejs.org](https://nodejs.org/)

#### 2. "Permission denied" (macOS/Linux)
```bash
sudo npm install -g npm@latest
```

#### 3. "Python not found"
**Solution**: Install Python and ensure it's in your PATH

#### 4. "PyInstaller not found"
```bash
pip install --upgrade pyinstaller
```

#### 5. Port 5173 already in use
```bash
# Kill the process using the port
npx kill-port 5173
# Or change the port in vite.config.ts
```

### Build Issues
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear build cache
rm -rf dist
npm run build
```

## 🎮 Using SecureBuilder v3.0

### First Launch
1. The application will open with a dark theme interface
2. Navigate to the **Dashboard** to see the overview
3. Go to **Project Builder** to create your first project
4. Use **Settings** to configure paths and preferences

### Creating Your First Build
1. Click **"New Project"** on the Dashboard
2. Browse and select your Python script
3. Configure build settings (app name, output path, etc.)
4. Choose build mode: Clean, Protected, or Obfuscated
5. Click **"Build EXE"** to create your executable

### Advanced Features
- **Wizard Mode**: Enable for guided building experience
- **Config Manager**: Manage economic configurations with F.I.S.H. rules
- **Build History**: Track all your builds and their status
- **Project Profiles**: Save and load project configurations

## 📊 Performance Tips

### Development
- Use `npm run dev` for fastest development experience
- Keep DevTools open to monitor performance
- Use React DevTools browser extension

### Production
- Always run `npm run build` before packaging
- Test the built version before distribution
- Use `npm run package` for final distribution

## 🔒 Security Notes

- The application uses secure Electron practices
- Context isolation is enabled
- Node integration is disabled in renderer
- All IPC communication is through secure preload scripts

## 📝 Development Commands

```bash
# Development
npm run dev              # Start dev server + Electron
npm run dev:react        # Start only React dev server
npm run dev:electron     # Start only Electron (requires React server)

# Building
npm run build            # Build both React and Electron
npm run build:react      # Build only React app
npm run build:electron   # Compile only Electron TypeScript

# Packaging
npm run package          # Package for current platform
npm run package:win      # Package for Windows
npm run package:mac      # Package for macOS
npm run package:linux    # Package for Linux

# Quality
npm run lint             # Run ESLint
npm run test             # Run tests (when implemented)
npm run preview          # Preview built app
```

## 🆘 Support

If you encounter issues:

1. **Check Prerequisites**: Ensure Node.js and Python are installed
2. **Review Logs**: Check terminal output for error messages
3. **Clear Cache**: Delete `node_modules` and reinstall
4. **Check Ports**: Ensure port 5173 is available
5. **Update Dependencies**: Run `npm update`

## 🎉 Success Indicators

You'll know SecureBuilder v3.0 is working when:
- ✅ Electron window opens with dark theme
- ✅ Navigation sidebar is visible
- ✅ Dashboard shows project overview
- ✅ Project Builder loads without errors
- ✅ File dialogs work for browsing scripts
- ✅ Build process can execute PyInstaller commands

## 🔄 Next Steps

After successful launch:
1. **Explore Features**: Try each page (Dashboard, Builder, Config, Settings)
2. **Test Building**: Create a simple Python script and build it
3. **Customize Settings**: Configure your preferred paths and options
4. **Save Projects**: Create and save project profiles
5. **Advanced Features**: Explore Wizard Mode and Config Manager

---

**Enjoy building with SecureBuilder v3.0!** 🎯
