import React, { useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Badge,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  FolderOpen as ProjectIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { toggleSidebar, setCurrentPage, addNotification } from '@/store/slices/uiSlice';

const drawerWidth = 240;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  const { sidebarOpen, notifications } = useSelector((state: RootState) => state.ui);
  const { currentProject } = useSelector((state: RootState) => state.project);
  
  const unreadNotifications = notifications.filter(n => !n.read).length;

  const menuItems = [
    { text: 'Dashboard', icon: <DashboardIcon />, path: '/' },
    { text: 'Project Builder', icon: <BuildIcon />, path: '/builder' },
    { text: 'Config Manager', icon: <ProjectIcon />, path: '/config' },
    { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
  ];

  useEffect(() => {
    // Set up Electron IPC listeners
    if (window.electronAPI) {
      const handleMenuAction = (action: string) => {
        switch (action) {
          case 'new-project':
            navigate('/builder');
            dispatch(addNotification({
              type: 'info',
              title: 'New Project',
              message: 'Creating new project...',
            }));
            break;
          case 'open-project':
            // Handle open project
            break;
          case 'save-project':
            // Handle save project
            break;
          case 'build-exe':
            if (currentProject) {
              dispatch(addNotification({
                type: 'info',
                title: 'Build Started',
                message: `Building ${currentProject.name}...`,
              }));
            }
            break;
          case 'about':
            dispatch(addNotification({
              type: 'info',
              title: 'About SecureBuilder',
              message: 'SecureBuilder v3.0 - Modern Python executable builder',
            }));
            break;
        }
      };

      window.electronAPI.on('menu-new-project', () => handleMenuAction('new-project'));
      window.electronAPI.on('menu-open-project', () => handleMenuAction('open-project'));
      window.electronAPI.on('menu-save-project', () => handleMenuAction('save-project'));
      window.electronAPI.on('menu-build-exe', () => handleMenuAction('build-exe'));
      window.electronAPI.on('menu-about', () => handleMenuAction('about'));

      return () => {
        window.electronAPI.removeAllListeners('menu-new-project');
        window.electronAPI.removeAllListeners('menu-open-project');
        window.electronAPI.removeAllListeners('menu-save-project');
        window.electronAPI.removeAllListeners('menu-build-exe');
        window.electronAPI.removeAllListeners('menu-about');
      };
    }
  }, [navigate, dispatch, currentProject]);

  useEffect(() => {
    // Update current page based on location
    const pathToPage: Record<string, string> = {
      '/': 'dashboard',
      '/builder': 'builder',
      '/config': 'config',
      '/settings': 'settings',
    };
    dispatch(setCurrentPage(pathToPage[location.pathname] || 'dashboard'));
  }, [location.pathname, dispatch]);

  const handleDrawerToggle = () => {
    dispatch(toggleSidebar());
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const drawer = (
    <div>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          SecureBuilder
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleNavigation(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: sidebarOpen ? `calc(100% - ${drawerWidth}px)` : '100%' },
          ml: { sm: sidebarOpen ? `${drawerWidth}px` : 0 },
          transition: 'width 0.3s, margin 0.3s',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {currentProject ? `${currentProject.name} - SecureBuilder` : 'SecureBuilder'}
          </Typography>
          <IconButton color="inherit">
            <Badge badgeContent={unreadNotifications} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: sidebarOpen ? drawerWidth : 0 }, flexShrink: { sm: 0 } }}
        aria-label="navigation"
      >
        <Drawer
          variant="persistent"
          open={sidebarOpen}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: sidebarOpen ? `calc(100% - ${drawerWidth}px)` : '100%' },
          ml: { sm: sidebarOpen ? 0 : `-${drawerWidth}px` },
          transition: 'width 0.3s, margin 0.3s',
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

export default Layout;
