#!/usr/bin/env python3
"""
Simple Console Test for SecureBuilder
=====================================
A basic console application to test SecureBuilder's build process
without GUI dependencies.
"""

import sys
import os
import platform
from datetime import datetime
import json

def display_banner():
    """Display application banner"""
    print("=" * 60)
    print("🛡️  SECUREBUILDER v3.0 - SIMPLE TEST APPLICATION")
    print("=" * 60)
    print()

def display_system_info():
    """Display system information"""
    print("📊 SYSTEM INFORMATION:")
    print(f"   • Python Version: {sys.version.split()[0]}")
    print(f"   • Platform: {platform.system()} {platform.release()}")
    print(f"   • Architecture: {platform.machine()}")
    print(f"   • Executable: {sys.executable}")
    print(f"   • Script Location: {__file__}")
    print()

def test_file_operations():
    """Test basic file operations"""
    print("🧪 TESTING FILE OPERATIONS:")
    
    try:
        # Test file creation
        test_file = "securebuilder_test.tmp"
        with open(test_file, 'w') as f:
            f.write(f"SecureBuilder Test - {datetime.now()}")
        print("   ✅ File creation: SUCCESS")
        
        # Test file reading
        with open(test_file, 'r') as f:
            content = f.read()
        print("   ✅ File reading: SUCCESS")
        
        # Test file deletion
        os.remove(test_file)
        print("   ✅ File deletion: SUCCESS")
        
    except Exception as e:
        print(f"   ❌ File operations: FAILED - {e}")
    
    print()

def test_imports():
    """Test common module imports"""
    print("📦 TESTING MODULE IMPORTS:")
    
    modules_to_test = [
        ('json', 'JSON processing'),
        ('datetime', 'Date/time handling'),
        ('os', 'Operating system interface'),
        ('sys', 'System-specific parameters'),
        ('platform', 'Platform identification'),
        ('subprocess', 'Process management'),
        ('hashlib', 'Cryptographic hashing'),
        ('uuid', 'UUID generation'),
    ]
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name}: {description}")
        except ImportError as e:
            print(f"   ❌ {module_name}: FAILED - {e}")
    
    print()

def test_json_operations():
    """Test JSON operations"""
    print("🔧 TESTING JSON OPERATIONS:")
    
    try:
        # Create test data
        test_data = {
            "app_name": "SecureBuilder Test",
            "version": "3.0",
            "build_time": datetime.now().isoformat(),
            "platform": platform.system(),
            "python_version": sys.version.split()[0],
            "features": ["console_app", "file_ops", "json_support"],
            "test_results": {
                "file_operations": True,
                "imports": True,
                "json_processing": True
            }
        }
        
        # Test JSON serialization
        json_string = json.dumps(test_data, indent=2)
        print("   ✅ JSON serialization: SUCCESS")
        
        # Test JSON deserialization
        parsed_data = json.loads(json_string)
        print("   ✅ JSON deserialization: SUCCESS")
        
        # Verify data integrity
        if parsed_data["app_name"] == test_data["app_name"]:
            print("   ✅ Data integrity: SUCCESS")
        else:
            print("   ❌ Data integrity: FAILED")
            
    except Exception as e:
        print(f"   ❌ JSON operations: FAILED - {e}")
    
    print()

def test_calculations():
    """Test mathematical calculations"""
    print("🔢 TESTING CALCULATIONS:")
    
    try:
        # Basic arithmetic
        result1 = 42 * 1337 + 2024
        print(f"   ✅ Arithmetic: 42 * 1337 + 2024 = {result1}")
        
        # String operations
        text = "SecureBuilder"
        result2 = text.upper() + " " + text.lower()
        print(f"   ✅ String ops: '{text}' -> '{result2}'")
        
        # List operations
        numbers = [1, 2, 3, 4, 5]
        result3 = sum(numbers) * len(numbers)
        print(f"   ✅ List ops: sum({numbers}) * len = {result3}")
        
    except Exception as e:
        print(f"   ❌ Calculations: FAILED - {e}")
    
    print()

def display_build_info():
    """Display build information"""
    print("🏗️  BUILD INFORMATION:")
    print(f"   • Built with: SecureBuilder v3.0 Professional")
    print(f"   • Build time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   • Executable size: {get_exe_size()}")
    print(f"   • Working directory: {os.getcwd()}")
    print()

def get_exe_size():
    """Get executable file size"""
    try:
        if hasattr(sys, 'frozen'):
            # Running as executable
            size = os.path.getsize(sys.executable)
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            # Running as script
            return "N/A (running as script)"
    except:
        return "Unknown"

def run_interactive_test():
    """Run interactive test menu"""
    print("🎮 INTERACTIVE TEST MENU:")
    print("   1. Run all tests again")
    print("   2. Display system info")
    print("   3. Test file operations")
    print("   4. Test calculations")
    print("   5. Exit")
    print()
    
    while True:
        try:
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == '1':
                print("\n" + "="*60)
                run_all_tests()
                print("="*60 + "\n")
            elif choice == '2':
                print()
                display_system_info()
            elif choice == '3':
                print()
                test_file_operations()
            elif choice == '4':
                print()
                test_calculations()
            elif choice == '5':
                print("\n👋 Thank you for testing SecureBuilder!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def run_all_tests():
    """Run all tests"""
    display_system_info()
    test_file_operations()
    test_imports()
    test_json_operations()
    test_calculations()
    display_build_info()

def main():
    """Main application entry point"""
    display_banner()
    
    print("🚀 Starting SecureBuilder Simple Test...")
    print()
    
    # Run all tests
    run_all_tests()
    
    # Success message
    print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
    print()
    print("🎉 This proves that SecureBuilder v3.0 can successfully")
    print("   create working executables from Python scripts!")
    print()
    
    # Interactive menu
    try:
        run_interactive_test()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")

if __name__ == "__main__":
    main()
