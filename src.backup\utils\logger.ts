// Client-side logging utility
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  component?: string;
}

class Logger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  log(level: LogLevel, message: string, data?: any, component?: string) {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      timestamp,
      level,
      message,
      data,
      component,
    };

    // Add to internal log storage
    this.logs.push(logEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with styling
    const style = this.getConsoleStyle(level);
    const componentStr = component ? `[${component}] ` : '';
    const logMessage = `[${timestamp}] [${level}] ${componentStr}${message}`;
    
    if (data) {
      console.log(`%c${logMessage}`, style, data);
    } else {
      console.log(`%c${logMessage}`, style);
    }

    // Store in localStorage for persistence
    try {
      const storedLogs = this.getStoredLogs();
      storedLogs.push(logEntry);
      
      // Keep only recent logs in storage
      const recentLogs = storedLogs.slice(-500);
      localStorage.setItem('securebuilder_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Failed to store log:', error);
    }
  }

  private getConsoleStyle(level: LogLevel): string {
    switch (level) {
      case 'DEBUG':
        return 'color: #888; font-size: 11px;';
      case 'INFO':
        return 'color: #2196F3; font-weight: normal;';
      case 'WARN':
        return 'color: #FF9800; font-weight: bold;';
      case 'ERROR':
        return 'color: #F44336; font-weight: bold; background: #ffebee; padding: 2px 4px;';
      default:
        return '';
    }
  }

  debug(message: string, data?: any, component?: string) {
    this.log('DEBUG', message, data, component);
  }

  info(message: string, data?: any, component?: string) {
    this.log('INFO', message, data, component);
  }

  warn(message: string, data?: any, component?: string) {
    this.log('WARN', message, data, component);
  }

  error(message: string, data?: any, component?: string) {
    this.log('ERROR', message, data, component);
  }

  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  getStoredLogs(): LogEntry[] {
    try {
      const stored = localStorage.getItem('securebuilder_logs');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored logs:', error);
      return [];
    }
  }

  clearLogs() {
    this.logs = [];
    localStorage.removeItem('securebuilder_logs');
  }

  exportLogs(): string {
    const allLogs = [...this.getStoredLogs(), ...this.logs];
    return allLogs.map(log => 
      `[${log.timestamp}] [${log.level}] ${log.component ? `[${log.component}] ` : ''}${log.message}${log.data ? ' ' + JSON.stringify(log.data) : ''}`
    ).join('\n');
  }
}

// Global logger instance
export const logger = new Logger();

// Global error handlers
window.addEventListener('error', (event) => {
  logger.error('Uncaught error', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error?.stack,
  }, 'Global');
});

window.addEventListener('unhandledrejection', (event) => {
  logger.error('Unhandled promise rejection', {
    reason: event.reason,
    promise: event.promise,
  }, 'Global');
});

// Log app startup
logger.info('SecureBuilder React app logger initialized');

export default logger;
