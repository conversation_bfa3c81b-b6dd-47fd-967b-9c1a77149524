
XconomyChooser – Secure Builder System (Technical Deep Dive)

---

## 1. Purpose

The Secure Builder system exists to provide a safe, verifiable, and reversible way to construct and deploy economic configuration changes to SCUM servers. It ensures that all modifications to economy JSON or INI files are:

- Authenticated and logged
- Reversible via deltas (diffs)
- Traceable through developer-tier logs
- Consistent with F.I.S.H.-guided rule applications

---

## 2. Core Capabilities

### ✅ Safe Construction
- All edits pass through patch builders that validate against schema and context
- Illegal operations (e.g., setting base_price to -1) are blocked with reason codes
- Supports dry-run mode with live preview of diffs

### 🔄 Reversible Logic
- All patches (edits, deletes, inserts) generate `PatchDelta` objects
- Undo/Redo support is built from these deltas
- A complete session state can be serialized to `.xbuild`

### 🔐 Authentication & Locking
- Files are locked once a patch session is opened
- Configurable user-level signing: developer name, timestamp, reason
- Optional signing certificate or hash validation for tamper detection

---

## 3. Architecture

### Core Classes
- `SecureBuilder`: Main orchestrator
- `PatchDelta`: Stores reversible mutation data
- `PatchValidator`: Rule-checks and schema enforcement
- `ChangeHistory`: Tracks undo/redo stacks

### Flow
```
Load Config → Create Patch Session → Apply Rule Logic → Generate Delta → Validate → Commit or Rollback
```

### Logging
- Each patch application logs via `@tracing`
- Logs are grouped under [TRACE:BUILDER] domain

---

## 4. Integration Points

- **XconomyChooser GUI**: All edits (via sliders, inputs, bulk tools) are routed through SecureBuilder
- **F.I.S.H. Logic**: Rules apply changes via `SecureBuilder.apply_patch(...)`
- **Undo/Redo Stack**: `PatchDelta` instances added to ChangeHistory
- **Export System**: Builds can be exported as finalized `.json` or `.xbuild` delta archive

---

## 5. Advanced Features

- Batch rule application across categories
- Preview mode: shows pre/post values before commit
- Delta visualizer (planned): show tree diff with color codes
- Session sealing (planned): lock + sign config batch for external audits

---

## 6. Developer Settings

| Environment Variable       | Effect                          |
|----------------------------|----------------------------------|
| `SECURE_BUILDER_DEBUG=1`   | Enables verbose logging         |
| `SECURE_BUILDER_TRACE=1`   | Logs each internal call         |
| `SECURE_BUILDER_READONLY=1`| Prevents actual file writes     |

---

## 7. Sample PatchDelta

```json
{
  "target": "Trader_A",
  "field": "base_price",
  "before": 1500,
  "after": 1950,
  "timestamp": "2025-06-09T15:33:42",
  "user": "dev_jason",
  "reason": "Seasonal markup"
}
```

---

## 8. Summary

Secure Builder is the backbone of safe economy manipulation in XconomyChooser. It enforces rules, maintains history, prevents corruption, and enables developers to trace and reverse every single change across the economic lifecycle.
