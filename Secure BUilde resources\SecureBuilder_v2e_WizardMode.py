import customtkinter as ctk
class WizardBuilder(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("SecureBuilder Wizard")
        self.geometry("720x400")
        self.choice = ctk.StringVar(value="Apprentice")
        ctk.CTkLabel(self, text="Select Wizard Level").pack(pady=10)
        ctk.CTkOptionMenu(self, values=["Apprentice", "Level 3 Wizard", "Epic Mage"], variable=self.choice).pack()
        ctk.CTkButton(self, text="Begin Ritual", command=self.cast).pack(pady=20)
        self.log = ctk.CTkTextbox(self, height=10)
        self.log.pack(fill="both", expand=True)

    def cast(self):
        lvl = self.choice.get()
        self.log.insert("end", f"🔮 Ritual begun at level: {lvl}\n")
        if lvl == "Epic Mage":
            self.log.insert("end", "✨ Applying Unlock, PyArmor, MSI, and ZIP enchantments...\n")
        elif lvl == "Level 3 Wizard":
            self.log.insert("end", "🧙 Applying Unlock and PyArmor...\n")
        else:
            self.log.insert("end", "🔰 Simple build...\n")

if __name__ == "__main__":
    WizardBuilder().mainloop()
