
import os
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import json
import subprocess
import zipfile
import tempfile
import datetime
import shutil
import platform

PROFILE_EXT = ".sbproj"
SECURE_BACKUP_DIR = os.path.join(os.path.expanduser("~"), "SecureBuilder_Backups")

class SecureBuilderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SecureBuilder v2")
        self.script_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.app_name = tk.StringVar()
        self.splash_path = tk.StringVar()
        self.icon_path = tk.StringVar()
        self.sound_path = tk.StringVar()
        self.zip_location = tk.StringVar()
        self.msi_location = tk.StringVar()
        self.wrapped_location = tk.StringVar()
        self.build_mode = tk.StringVar(value="Clean Only")
        self.protect_enabled = tk.BooleanVar(value=False)
        self.protected_output_mode = tk.StringVar(value="Secure Only")
        self.generate_msi_var = tk.BooleanVar(value=False)
        self.generate_zip_var = tk.BooleanVar(value=False)
        self.dev_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_level = tk.StringVar(value="Apprentice")
        self.wizard_label_var = tk.StringVar()
        self.create_widgets()

    def create_widgets(self):
        frame = ctk.CTkFrame(self.root)
        frame.pack(padx=20, pady=20)
        padding = dict(padx=5, pady=4)

        # Script path
        ctk.CTkLabel(frame, text="Python Script:").grid(row=0, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.script_path, width=300).grid(row=0, column=1, **padding)
        ctk.CTkButton(frame, text="Browse", command=self.browse_script).grid(row=0, column=2, **padding)

        # Output folder
        ctk.CTkLabel(frame, text="Output Folder:").grid(row=1, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.output_folder, width=250).grid(row=1, column=1, **padding)
        ctk.CTkButton(frame, text="Choose", command=self.choose_output_folder).grid(row=1, column=2, **padding)
        ctk.CTkButton(frame, text="Browse Output Folder", command=self.browse_output_folder).grid(row=1, column=3, **padding)

        # App name
        ctk.CTkLabel(frame, text="App Name:").grid(row=2, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.app_name, width=300).grid(row=2, column=1, columnspan=3, **padding)

        # Options and browse buttons
        ctk.CTkCheckBox(frame, text="Enable ZIP Bundling", variable=self.generate_zip_var).grid(row=3, column=1, sticky="w", **padding)
        ctk.CTkButton(frame, text="Browse ZIP Location", command=self.browse_zip_location).grid(row=3, column=0, sticky="w", **padding)

        ctk.CTkCheckBox(frame, text="Enable MSI Generation", variable=self.generate_msi_var).grid(row=4, column=1, sticky="w", **padding)
        ctk.CTkButton(frame, text="Browse MSI Location", command=self.browse_msi_location).grid(row=4, column=0, sticky="w", **padding)

        ctk.CTkCheckBox(frame, text="Enable Protection", variable=self.protect_enabled).grid(row=5, column=1, sticky="w", **padding)
        ctk.CTkButton(frame, text="Browse Wrapped Location", command=self.browse_wrapped_location).grid(row=5, column=0, sticky="w", **padding)

        # Build button
        ctk.CTkButton(frame, text="Build EXE", command=self.build_exe).grid(row=6, column=1, pady=10)

        # Wizard mode
        ctk.CTkCheckBox(frame, text="Enable Wizard Mode", variable=self.wizard_mode_enabled, command=self.toggle_wizard_mode).grid(row=7, column=1, sticky="w", **padding)
        self.wizard_frame = ctk.CTkFrame(frame)
        self.wizard_frame.grid(row=8, column=0, columnspan=4, pady=6)
        self.wizard_frame.grid_remove()
        ctk.CTkLabel(self.wizard_frame, text="Wizard Level:").grid(row=0, column=0, sticky="w", **padding)
        ctk.CTkOptionMenu(self.wizard_frame, variable=self.wizard_level, values=["Apprentice","Level 3 Wizard","Epic Mage"]).grid(row=0, column=1, **padding)
        def update_icon():
            mapping={{"Apprentice":"🪄","Level 3 Wizard":"🎩","Epic Mage":"🌌"}}
            self.wizard_label_var.set(f"{{mapping.get(self.wizard_level.get(),'🪄')}} Begin Wizard")
        update_icon()
        self.wizard_level.trace_add("write", lambda *a: update_icon())
        ctk.CTkButton(self.wizard_frame, textvariable=self.wizard_label_var, command=self.start_wizard).grid(row=1,column=1,pady=6)

    # Browse methods
    def browse_script(self): 
        p=filedialog.askopenfilename(filetypes=[("Python Files","*.py")])
        p and self.script_path.set(p)
    def choose_output_folder(self):
        d=filedialog.askdirectory()
        d and self.output_folder.set(d)
    def browse_output_folder(self):
        d=filedialog.askdirectory()
        d and self.output_folder.set(d)
    def browse_zip_location(self):
        d=filedialog.askdirectory()
        d and self.zip_location.set(d)
    def browse_msi_location(self):
        d=filedialog.askdirectory()
        d and self.msi_location.set(d)
    def browse_wrapped_location(self):
        d=filedialog.askdirectory()
        d and self.wrapped_location.set(d)

    # Rest of methods are assumed unchanged (inject_unlock_wrapper, create_dev_unlock_tool, wrap_script_with_unlock, build_exe, start_wizard, save_profile, load_profile)

if __name__=="__main__":
    ctk.set_appearance_mode("dark")
    root=ctk.CTk()
    app=SecureBuilderApp(root)
    root.mainloop()
