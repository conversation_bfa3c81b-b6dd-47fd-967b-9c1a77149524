import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface BuildLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
}

export interface BuildProgress {
  stage: string;
  progress: number; // 0-100
  message: string;
}

interface BuildState {
  isBuilding: boolean;
  buildLogs: BuildLog[];
  buildProgress: BuildProgress | null;
  lastBuildResult: {
    success: boolean;
    timestamp: string;
    outputPath?: string;
    error?: string;
  } | null;
  buildHistory: Array<{
    id: string;
    projectName: string;
    timestamp: string;
    success: boolean;
    buildMode: string;
    outputPath?: string;
  }>;
}

const initialState: BuildState = {
  isBuilding: false,
  buildLogs: [],
  buildProgress: null,
  lastBuildResult: null,
  buildHistory: [],
};

const buildSlice = createSlice({
  name: 'build',
  initialState,
  reducers: {
    startBuild: (state) => {
      state.isBuilding = true;
      state.buildLogs = [];
      state.buildProgress = {
        stage: 'Initializing',
        progress: 0,
        message: 'Starting build process...',
      };
    },
    addBuildLog: (state, action: PayloadAction<Omit<BuildLog, 'id' | 'timestamp'>>) => {
      const log: BuildLog = {
        ...action.payload,
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
      };
      state.buildLogs.push(log);
    },
    updateBuildProgress: (state, action: PayloadAction<BuildProgress>) => {
      state.buildProgress = action.payload;
    },
    completeBuild: (state, action: PayloadAction<{
      success: boolean;
      outputPath?: string;
      error?: string;
      projectName: string;
      buildMode: string;
    }>) => {
      state.isBuilding = false;
      state.buildProgress = null;
      
      const timestamp = new Date().toISOString();
      state.lastBuildResult = {
        success: action.payload.success,
        timestamp,
        outputPath: action.payload.outputPath,
        error: action.payload.error,
      };

      // Add to build history
      state.buildHistory.unshift({
        id: `build_${Date.now()}`,
        projectName: action.payload.projectName,
        timestamp,
        success: action.payload.success,
        buildMode: action.payload.buildMode,
        outputPath: action.payload.outputPath,
      });

      // Keep only last 50 builds
      state.buildHistory = state.buildHistory.slice(0, 50);
    },
    cancelBuild: (state) => {
      state.isBuilding = false;
      state.buildProgress = null;
      state.lastBuildResult = {
        success: false,
        timestamp: new Date().toISOString(),
        error: 'Build cancelled by user',
      };
    },
    clearBuildLogs: (state) => {
      state.buildLogs = [];
    },
    clearBuildHistory: (state) => {
      state.buildHistory = [];
    },
  },
});

export const {
  startBuild,
  addBuildLog,
  updateBuildProgress,
  completeBuild,
  cancelBuild,
  clearBuildLogs,
  clearBuildHistory,
} = buildSlice.actions;

export default buildSlice.reducer;
