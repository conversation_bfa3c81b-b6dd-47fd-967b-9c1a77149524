#!/usr/bin/env python3
"""
SecureBuilder v3.0 Professional
================================
Modern CustomTkinter GUI for Python executable building
Features: Professional UI, Dark/Light themes, Advanced options, Project management
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinterdnd2 import DND_FILES, TkinterDnD
import os
import subprocess
import json
import threading
from datetime import datetime
import sys
import zipfile
import shutil
from builtin_test_projects import TEST_PROJECTS

# Set appearance and theme
ctk.set_appearance_mode("dark")  # "dark" or "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class ToolTip:
    """Create a tooltip for a given widget"""
    def __init__(self, widget, text='widget info'):
        self.widget = widget
        self.text = text
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.tipwindow = None

    def enter(self, event=None):
        self.show_tooltip()

    def leave(self, event=None):
        self.hide_tooltip()

    def show_tooltip(self):
        if self.tipwindow or not self.text:
            return
        x, y, cx, cy = self.widget.bbox("insert")
        x = x + self.widget.winfo_rootx() + 25
        y = y + cy + self.widget.winfo_rooty() + 25
        self.tipwindow = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry("+%d+%d" % (x, y))
        label = tk.Label(tw, text=self.text, justify=tk.LEFT,
                        background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                        font=("tahoma", "8", "normal"))
        label.pack(ipadx=1)

    def hide_tooltip(self):
        tw = self.tipwindow
        self.tipwindow = None
        if tw:
            tw.destroy()

class SecureBuilderPro(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # Window configuration
        self.title("🛡️ SecureBuilder v3.0 Professional")
        self.geometry("1000x700")
        self.minsize(800, 600)
        
        # Configure grid weights
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # Variables
        self.setup_variables()
        
        # Create UI
        self.create_sidebar()
        self.create_main_frame()
        self.create_status_bar()
        
        # Load settings
        self.load_settings()
        
    def setup_variables(self):
        """Initialize all application variables"""
        # File paths
        self.script_path = ctk.StringVar()
        self.icon_path = ctk.StringVar()
        self.output_dir = ctk.StringVar(value="dist")
        self.exe_name = ctk.StringVar()
        
        # Build options
        self.use_onefile = ctk.BooleanVar(value=True)
        self.use_console = ctk.BooleanVar(value=False)
        self.use_pyarmor = ctk.BooleanVar(value=False)
        self.use_upx = ctk.BooleanVar(value=False)
        self.create_zip = ctk.BooleanVar(value=True)
        
        # Advanced options
        self.add_data = ctk.StringVar()
        self.hidden_imports = ctk.StringVar()
        self.exclude_modules = ctk.StringVar()

        # Security options
        self.use_security_wrapper = ctk.BooleanVar(value=False)
        self.security_key = ctk.StringVar()

        # Module selection
        self.build_tool = ctk.StringVar(value="PyInstaller")  # PyInstaller, Auto-py-to-exe, cx_Freeze
        self.use_pyarmor_standalone = ctk.BooleanVar(value=False)
        self.use_upx_standalone = ctk.BooleanVar(value=False)
        self.use_nuitka = ctk.BooleanVar(value=False)

        # Project management
        self.current_project = None
        
    def create_sidebar(self):
        """Create the left sidebar with navigation"""
        self.sidebar_frame = ctk.CTkFrame(self, width=200, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=2, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(10, weight=1)
        
        # Logo/Title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="🛡️ SecureBuilder",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        self.version_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="v3.0 Professional",
            font=ctk.CTkFont(size=12)
        )
        self.version_label.grid(row=1, column=0, padx=20, pady=(0, 20))
        
        # Navigation buttons
        self.nav_buttons = []
        nav_items = [
            ("📁 Project", self.show_project_tab),
            ("🧪 Test Projects", self.show_test_projects_tab),
            ("🛠️ Build Tools", self.show_build_tools_tab),
            ("⚙️ Build Settings", self.show_build_tab),
            ("🔧 Advanced", self.show_advanced_tab),
            ("📊 Output", self.show_output_tab),
        ]
        
        for i, (text, command) in enumerate(nav_items):
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                command=command,
                height=36,
                font=ctk.CTkFont(size=13),
                corner_radius=6,
                border_width=1
            )
            btn.grid(row=i+2, column=0, padx=15, pady=8, sticky="ew")
            self.nav_buttons.append(btn)
        
        # Theme toggle
        self.theme_label = ctk.CTkLabel(self.sidebar_frame, text="Appearance:")
        self.theme_label.grid(row=8, column=0, padx=20, pady=(20, 0))
        
        self.theme_menu = ctk.CTkOptionMenu(
            self.sidebar_frame,
            values=["Dark", "Light", "System"],
            command=self.change_theme
        )
        self.theme_menu.grid(row=9, column=0, padx=20, pady=10, sticky="ew")
        
        # Quick actions
        self.quick_build_btn = ctk.CTkButton(
            self.sidebar_frame,
            text="Quick Build",
            command=self.quick_build,
            height=40,
            font=ctk.CTkFont(size=13, weight="bold"),
            corner_radius=8,
            border_width=1
        )
        self.quick_build_btn.grid(row=11, column=0, padx=15, pady=20, sticky="ew")
        
    def create_main_frame(self):
        """Create the main content area"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=20, pady=20)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # Create tabbed content
        self.create_project_tab()
        self.create_test_projects_tab()
        self.create_build_tools_tab()
        self.create_build_tab()
        self.create_advanced_tab()
        self.create_output_tab()

        # Show project tab by default
        self.show_project_tab()
        
    def create_project_tab(self):
        """Create project management tab"""
        self.project_frame = ctk.CTkFrame(self.main_frame)
        
        # Title
        title = ctk.CTkLabel(
            self.project_frame,
            text="📁 Project Configuration",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # Project file selection
        file_frame = ctk.CTkFrame(self.project_frame)
        file_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(file_frame, text="Python Script:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        script_frame = ctk.CTkFrame(file_frame)
        script_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.script_entry = ctk.CTkEntry(script_frame, textvariable=self.script_path, height=35, placeholder_text="Drag & drop Python file here or click Browse")
        self.script_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)

        # Add drag-and-drop support
        try:
            self.script_entry.drop_target_register(DND_FILES)
            self.script_entry.dnd_bind('<<Drop>>', self.on_script_drop)
        except:
            pass  # Fallback if drag-and-drop not available
        
        browse_btn = ctk.CTkButton(
            script_frame,
            text="Browse",
            command=self.browse_script,
            width=100,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        browse_btn.pack(side="right", padx=(5, 10), pady=10)

        # Add tooltips
        ToolTip(self.script_entry, "Select the Python script to convert to executable.\nSupports drag & drop!")
        ToolTip(browse_btn, "Browse for a Python (.py) file to build")
        
        # Output configuration
        output_frame = ctk.CTkFrame(self.project_frame)
        output_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(output_frame, text="Output Configuration:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Executable name
        name_frame = ctk.CTkFrame(output_frame)
        name_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(name_frame, text="Executable Name:").pack(side="left", padx=10, pady=10)
        name_entry = ctk.CTkEntry(name_frame, textvariable=self.exe_name, placeholder_text="MyApp")
        name_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)
        
        # Output directory
        dir_frame = ctk.CTkFrame(output_frame)
        dir_frame.pack(fill="x", padx=10, pady=(5, 10))
        
        ctk.CTkLabel(dir_frame, text="Output Directory:").pack(side="left", padx=10, pady=10)
        dir_entry = ctk.CTkEntry(dir_frame, textvariable=self.output_dir)
        dir_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)
        
        # Icon selection
        icon_frame = ctk.CTkFrame(self.project_frame)
        icon_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(icon_frame, text="Application Icon (optional):", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        icon_select_frame = ctk.CTkFrame(icon_frame)
        icon_select_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.icon_entry = ctk.CTkEntry(icon_select_frame, textvariable=self.icon_path, placeholder_text="Select .ico file")
        self.icon_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        icon_btn = ctk.CTkButton(
            icon_select_frame,
            text="Browse",
            command=self.browse_icon,
            width=100,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        icon_btn.pack(side="right", padx=(5, 10), pady=10)

        # Quick Build section on main page
        quick_build_section = ctk.CTkFrame(self.project_frame)
        quick_build_section.pack(fill="x", padx=20, pady=20)

        ctk.CTkLabel(
            quick_build_section,
            text="Ready to Build?",
            font=ctk.CTkFont(size=15, weight="bold")
        ).pack(pady=(15, 5))

        ctk.CTkLabel(
            quick_build_section,
            text="Build your executable with current settings",
            font=ctk.CTkFont(size=12)
        ).pack(pady=(0, 15))

        # Enhanced Quick Build button
        self.main_quick_build_btn = ctk.CTkButton(
            quick_build_section,
            text="Build Executable",
            command=self.quick_build,
            width=200,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            corner_radius=8,
            border_width=1
        )
        self.main_quick_build_btn.pack(pady=(0, 20))

        # Status indicator
        self.build_status_label = ctk.CTkLabel(
            quick_build_section,
            text="✅ Ready to build",
            font=ctk.CTkFont(size=12),
            text_color=("green", "lightgreen")
        )
        self.build_status_label.pack(pady=(0, 15))

    def create_test_projects_tab(self):
        """Create test projects tab"""
        self.test_projects_frame = ctk.CTkFrame(self.main_frame)

        # Title
        title = ctk.CTkLabel(
            self.test_projects_frame,
            text="Built-in Test Projects",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=20)

        # Description
        desc = ctk.CTkLabel(
            self.test_projects_frame,
            text="Test SecureBuilder with pre-built applications. Click 'Generate & Build' to create the .py file and build it.",
            font=ctk.CTkFont(size=12),
            wraplength=600
        )
        desc.pack(pady=(0, 20))

        # Test projects container
        projects_container = ctk.CTkScrollableFrame(self.test_projects_frame)
        projects_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Create project cards
        for project_id, project_data in TEST_PROJECTS.items():
            self.create_project_card(projects_container, project_id, project_data)

    def create_project_card(self, parent, project_id, project_data):
        """Create a project card"""
        # Project frame
        project_frame = ctk.CTkFrame(parent)
        project_frame.pack(fill="x", pady=10, padx=10)

        # Project header
        header_frame = ctk.CTkFrame(project_frame)
        header_frame.pack(fill="x", padx=15, pady=15)

        # Project name and description
        name_label = ctk.CTkLabel(
            header_frame,
            text=project_data["name"],
            font=ctk.CTkFont(size=16, weight="bold")
        )
        name_label.pack(anchor="w")

        desc_label = ctk.CTkLabel(
            header_frame,
            text=project_data["description"],
            font=ctk.CTkFont(size=12),
            wraplength=500
        )
        desc_label.pack(anchor="w", pady=(5, 0))

        # Requirements
        req_text = f"Requirements: {', '.join(project_data['requirements'])}"
        req_label = ctk.CTkLabel(
            header_frame,
            text=req_text,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        req_label.pack(anchor="w", pady=(5, 0))

        # Buttons frame
        buttons_frame = ctk.CTkFrame(project_frame)
        buttons_frame.pack(fill="x", padx=15, pady=(0, 15))

        # Generate button
        generate_btn = ctk.CTkButton(
            buttons_frame,
            text="Generate .py File",
            command=lambda: self.generate_test_project(project_id),
            width=130,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        generate_btn.pack(side="left", padx=8, pady=10)

        # Generate and build button
        build_btn = ctk.CTkButton(
            buttons_frame,
            text="Generate & Build",
            command=lambda: self.generate_and_build_test_project(project_id),
            width=140,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        build_btn.pack(side="left", padx=8, pady=10)

        # View code button
        view_btn = ctk.CTkButton(
            buttons_frame,
            text="View Code",
            command=lambda: self.view_test_project_code(project_id),
            width=100,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        view_btn.pack(side="left", padx=8, pady=10)

        # Add tooltips
        ToolTip(generate_btn, f"Generate {project_data['filename']} in current directory")
        ToolTip(build_btn, f"Generate the .py file and immediately build it with optimal settings")
        ToolTip(view_btn, f"Preview the source code for {project_data['name']}")

    def create_build_tools_tab(self):
        """Create build tools selection tab"""
        self.build_tools_frame = ctk.CTkFrame(self.main_frame)

        # Title
        title = ctk.CTkLabel(
            self.build_tools_frame,
            text="Build Tools & Modules",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=20)

        # Description
        desc = ctk.CTkLabel(
            self.build_tools_frame,
            text="Select which tools and modules to use for building your executable.",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=(0, 20))

        # Primary build tool selection
        primary_frame = ctk.CTkFrame(self.build_tools_frame)
        primary_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(primary_frame, text="Primary Build Tool:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        build_tool_menu = ctk.CTkOptionMenu(
            primary_frame,
            values=["PyInstaller", "Auto-py-to-exe", "cx_Freeze", "Nuitka"],
            variable=self.build_tool
        )
        build_tool_menu.pack(anchor="w", padx=20, pady=10)
        ToolTip(build_tool_menu, "Select the primary tool for building executables")

        # Additional modules
        modules_frame = ctk.CTkFrame(self.build_tools_frame)
        modules_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(modules_frame, text="Additional Modules:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        module_options = [
            ("PyArmor (Code Obfuscation)", self.use_pyarmor_standalone, "Obfuscate Python code for protection"),
            ("UPX (Executable Compression)", self.use_upx_standalone, "Compress executable to reduce file size"),
            ("Nuitka (Alternative Compiler)", self.use_nuitka, "Use Nuitka as alternative Python compiler")
        ]

        for text, var, tooltip in module_options:
            checkbox = ctk.CTkCheckBox(
                modules_frame,
                text=text,
                variable=var,
                font=ctk.CTkFont(size=12),
                checkbox_width=16,
                checkbox_height=16,
                border_width=1,
                corner_radius=3
            )
            checkbox.pack(anchor="w", padx=20, pady=3)
            ToolTip(checkbox, tooltip)

        # Tool status and installation
        status_frame = ctk.CTkFrame(self.build_tools_frame)
        status_frame.pack(fill="both", expand=True, padx=20, pady=10)

        ctk.CTkLabel(status_frame, text="Tool Status & Installation:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        # Check tools button
        check_btn = ctk.CTkButton(
            status_frame,
            text="Check Tool Status",
            command=self.check_tool_status,
            width=150,
            height=32,
            font=ctk.CTkFont(size=12),
            corner_radius=6,
            border_width=1
        )
        check_btn.pack(anchor="w", padx=20, pady=10)

        # Status display
        self.tool_status_text = ctk.CTkTextbox(status_frame, height=200)
        self.tool_status_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def create_build_tab(self):
        """Create build settings tab"""
        self.build_frame = ctk.CTkFrame(self.main_frame)
        
        # Title
        title = ctk.CTkLabel(
            self.build_frame,
            text="⚙️ Build Settings",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # Basic options
        basic_frame = ctk.CTkFrame(self.build_frame)
        basic_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(basic_frame, text="Basic Options:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Checkboxes for basic options
        options = [
            ("One File (--onefile)", self.use_onefile, "Create a single executable file instead of a folder"),
            ("Show Console (--console)", self.use_console, "Show console window when running the executable"),
            ("Create ZIP Archive", self.create_zip, "Automatically create a ZIP file of the build output"),
        ]

        for text, var, tooltip in options:
            checkbox = ctk.CTkCheckBox(
                basic_frame,
                text=text,
                variable=var,
                font=ctk.CTkFont(size=12),
                checkbox_width=16,
                checkbox_height=16,
                border_width=1,
                corner_radius=3
            )
            checkbox.pack(anchor="w", padx=20, pady=3)
            ToolTip(checkbox, tooltip)
        
        # Advanced options
        advanced_frame = ctk.CTkFrame(self.build_frame)
        advanced_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(advanced_frame, text="Advanced Options:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))
        
        advanced_options = [
            ("PyArmor Obfuscation", self.use_pyarmor),
            ("UPX Compression", self.use_upx),
            ("Security Wrapper™", self.use_security_wrapper),
        ]

        for text, var in advanced_options:
            checkbox = ctk.CTkCheckBox(
                advanced_frame,
                text=text,
                variable=var,
                font=ctk.CTkFont(size=12),
                checkbox_width=16,
                checkbox_height=16,
                border_width=1,
                corner_radius=3
            )
            checkbox.pack(anchor="w", padx=20, pady=3)

        # Security key entry (only shown when Security Wrapper™ is enabled)
        security_frame = ctk.CTkFrame(advanced_frame)
        security_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(security_frame, text="Security Key (optional):").pack(side="left", padx=10, pady=10)
        security_entry = ctk.CTkEntry(security_frame, textvariable=self.security_key, placeholder_text="Leave empty for auto-generated key", show="*")
        security_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)
        
    def create_advanced_tab(self):
        """Create advanced settings tab"""
        self.advanced_frame = ctk.CTkFrame(self.main_frame)

        # Title
        title = ctk.CTkLabel(
            self.advanced_frame,
            text="🔧 Advanced Configuration",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)

        # PyInstaller Advanced Options
        pyi_frame = ctk.CTkFrame(self.advanced_frame)
        pyi_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(pyi_frame, text="PyInstaller Advanced Options:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        # Hidden imports
        hidden_frame = ctk.CTkFrame(pyi_frame)
        hidden_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(hidden_frame, text="Hidden Imports:").pack(side="left", padx=10, pady=10)
        hidden_entry = ctk.CTkEntry(hidden_frame, textvariable=self.hidden_imports, placeholder_text="module1,module2,module3")
        hidden_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)

        # Exclude modules
        exclude_frame = ctk.CTkFrame(pyi_frame)
        exclude_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(exclude_frame, text="Exclude Modules:").pack(side="left", padx=10, pady=10)
        exclude_entry = ctk.CTkEntry(exclude_frame, textvariable=self.exclude_modules, placeholder_text="tkinter,matplotlib")
        exclude_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)

        # Add data files
        data_frame = ctk.CTkFrame(pyi_frame)
        data_frame.pack(fill="x", padx=10, pady=(5, 10))

        ctk.CTkLabel(data_frame, text="Add Data Files:").pack(side="left", padx=10, pady=10)
        data_entry = ctk.CTkEntry(data_frame, textvariable=self.add_data, placeholder_text="data/*:data/")
        data_entry.pack(side="right", fill="x", expand=True, padx=10, pady=10)

        # Project Management
        project_frame = ctk.CTkFrame(self.advanced_frame)
        project_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(project_frame, text="Project Management:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=10, pady=(10, 5))

        project_buttons_frame = ctk.CTkFrame(project_frame)
        project_buttons_frame.pack(fill="x", padx=10, pady=(0, 10))

        save_project_btn = ctk.CTkButton(project_buttons_frame, text="💾 Save Project", command=self.save_project, width=120)
        save_project_btn.pack(side="left", padx=10, pady=10)

        load_project_btn = ctk.CTkButton(project_buttons_frame, text="📂 Load Project", command=self.load_project, width=120)
        load_project_btn.pack(side="left", padx=10, pady=10)

        new_project_btn = ctk.CTkButton(project_buttons_frame, text="📄 New Project", command=self.new_project, width=120)
        new_project_btn.pack(side="left", padx=10, pady=10)
        
    def create_output_tab(self):
        """Create output/log tab"""
        self.output_frame = ctk.CTkFrame(self.main_frame)
        
        # Title
        title = ctk.CTkLabel(
            self.output_frame,
            text="📊 Build Output",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # Output text area
        self.output_text = ctk.CTkTextbox(self.output_frame, height=400)
        self.output_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Clear button
        clear_btn = ctk.CTkButton(
            self.output_frame,
            text="Clear Output",
            command=self.clear_output,
            width=120
        )
        clear_btn.pack(pady=(0, 20))
        
    def create_status_bar(self):
        """Create status bar at bottom"""
        self.status_frame = ctk.CTkFrame(self, height=30)
        self.status_frame.grid(row=1, column=1, sticky="ew", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        self.progress_bar = ctk.CTkProgressBar(self.status_frame, width=200)
        self.progress_bar.pack(side="right", padx=10, pady=5)
        self.progress_bar.set(0)
        
    # Navigation methods
    def show_project_tab(self):
        """Show project configuration tab"""
        self.hide_all_tabs()
        self.project_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(0)

    def show_test_projects_tab(self):
        """Show test projects tab"""
        self.hide_all_tabs()
        self.test_projects_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(1)

    def show_build_tools_tab(self):
        """Show build tools tab"""
        self.hide_all_tabs()
        self.build_tools_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(2)

    def show_build_tab(self):
        """Show build settings tab"""
        self.hide_all_tabs()
        self.build_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(3)

    def show_advanced_tab(self):
        """Show advanced settings tab"""
        self.hide_all_tabs()
        self.advanced_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(4)

    def show_output_tab(self):
        """Show output tab"""
        self.hide_all_tabs()
        self.output_frame.pack(fill="both", expand=True)
        self.update_nav_buttons(5)

    def hide_all_tabs(self):
        """Hide all tab frames"""
        for frame in [self.project_frame, self.test_projects_frame, self.build_tools_frame,
                     self.build_frame, self.advanced_frame, self.output_frame]:
            frame.pack_forget()

    def update_nav_buttons(self, active_index):
        """Update navigation button appearance"""
        for i, btn in enumerate(self.nav_buttons):
            if i == active_index:
                btn.configure(fg_color=("gray75", "gray25"))
            else:
                btn.configure(fg_color=("gray85", "gray15"))
    
    # File dialog methods
    def browse_script(self):
        """Browse for Python script"""
        filename = filedialog.askopenfilename(
            title="Select Python Script",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if filename:
            self.script_path.set(filename)
            # Auto-set exe name based on script name
            if not self.exe_name.get():
                base_name = os.path.splitext(os.path.basename(filename))[0]
                self.exe_name.set(base_name)
    
    def browse_icon(self):
        """Browse for icon file"""
        filename = filedialog.askopenfilename(
            title="Select Icon File",
            filetypes=[("Icon files", "*.ico"), ("All files", "*.*")]
        )
        if filename:
            self.icon_path.set(filename)

    def on_script_drop(self, event):
        """Handle drag-and-drop of Python files"""
        try:
            files = self.script_entry.tk.splitlist(event.data)
            if files:
                file_path = files[0]
                if file_path.lower().endswith('.py'):
                    self.script_path.set(file_path)
                    # Auto-set exe name based on script name
                    if not self.exe_name.get():
                        base_name = os.path.splitext(os.path.basename(file_path))[0]
                        self.exe_name.set(base_name)
                    self.log_message(f"📁 Script loaded via drag-and-drop: {os.path.basename(file_path)}")
                else:
                    messagebox.showwarning("Invalid File", "Please drop a Python (.py) file.")
        except Exception as e:
            self.log_message(f"❌ Error handling dropped file: {str(e)}")

    # Test project methods
    def generate_test_project(self, project_id):
        """Generate a test project .py file"""
        try:
            project_data = TEST_PROJECTS[project_id]
            filename = project_data["filename"]

            # Write the project file
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(project_data["code"])

            self.log_message(f"📝 Generated test project: {filename}")
            messagebox.showinfo("Success", f"Test project '{project_data['name']}' generated as {filename}")

            # Auto-load into project settings
            self.script_path.set(os.path.abspath(filename))
            self.exe_name.set(os.path.splitext(filename)[0])

            # Apply recommended build options
            build_opts = project_data["build_options"]
            self.use_onefile.set(build_opts.get("use_onefile", True))
            self.use_console.set(build_opts.get("use_console", False))
            if "hidden_imports" in build_opts:
                self.hidden_imports.set(",".join(build_opts["hidden_imports"]))

            self.log_message(f"⚙️ Applied recommended build settings for {project_data['name']}")

        except Exception as e:
            self.log_message(f"❌ Error generating test project: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate test project: {str(e)}")

    def generate_and_build_test_project(self, project_id):
        """Generate test project and immediately build it"""
        try:
            # First generate the project
            self.generate_test_project(project_id)

            # Switch to output tab
            self.show_output_tab()

            # Start build
            self.log_message(f"🚀 Starting build for test project: {TEST_PROJECTS[project_id]['name']}")
            threading.Thread(target=self.run_build, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ Error in generate and build: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate and build: {str(e)}")

    def view_test_project_code(self, project_id):
        """View test project source code"""
        try:
            project_data = TEST_PROJECTS[project_id]

            # Create code viewer window
            code_window = ctk.CTkToplevel(self)
            code_window.title(f"Source Code - {project_data['name']}")
            code_window.geometry("800x600")

            # Code text area
            code_text = ctk.CTkTextbox(code_window)
            code_text.pack(fill="both", expand=True, padx=20, pady=20)

            # Insert code
            code_text.insert("1.0", project_data["code"])

            # Make read-only
            code_text.configure(state="disabled")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to view code: {str(e)}")

    def check_tool_status(self):
        """Check status of build tools"""
        try:
            self.tool_status_text.delete("1.0", "end")
            status_lines = []

            status_lines.append("🔍 CHECKING BUILD TOOL STATUS")
            status_lines.append("=" * 50)
            status_lines.append("")

            # Check PyInstaller
            try:
                result = subprocess.run(["pyinstaller", "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    status_lines.append(f"✅ PyInstaller: {version}")
                else:
                    status_lines.append("❌ PyInstaller: Not working properly")
            except FileNotFoundError:
                status_lines.append("❌ PyInstaller: Not installed")
                status_lines.append("   Install with: pip install pyinstaller")
            except Exception as e:
                status_lines.append(f"❌ PyInstaller: Error - {e}")

            # Check Auto-py-to-exe
            try:
                result = subprocess.run(["auto-py-to-exe", "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    status_lines.append("✅ Auto-py-to-exe: Available")
                else:
                    status_lines.append("❌ Auto-py-to-exe: Not working")
            except FileNotFoundError:
                status_lines.append("❌ Auto-py-to-exe: Not installed")
                status_lines.append("   Install with: pip install auto-py-to-exe")
            except Exception as e:
                status_lines.append(f"❌ Auto-py-to-exe: Error - {e}")

            # Check cx_Freeze
            try:
                import cx_Freeze
                status_lines.append(f"✅ cx_Freeze: {cx_Freeze.version}")
            except ImportError:
                status_lines.append("❌ cx_Freeze: Not installed")
                status_lines.append("   Install with: pip install cx_Freeze")

            # Check Nuitka
            try:
                result = subprocess.run(["nuitka", "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    status_lines.append(f"✅ Nuitka: {version}")
                else:
                    status_lines.append("❌ Nuitka: Not working properly")
            except FileNotFoundError:
                status_lines.append("❌ Nuitka: Not installed")
                status_lines.append("   Install with: pip install nuitka")
            except Exception as e:
                status_lines.append(f"❌ Nuitka: Error - {e}")

            # Check PyArmor
            try:
                result = subprocess.run(["pyarmor", "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    status_lines.append(f"✅ PyArmor: {version}")
                else:
                    status_lines.append("❌ PyArmor: Not working properly")
            except FileNotFoundError:
                status_lines.append("❌ PyArmor: Not installed")
                status_lines.append("   Install with: pip install pyarmor")
            except Exception as e:
                status_lines.append(f"❌ PyArmor: Error - {e}")

            # Check UPX
            try:
                result = subprocess.run(["upx", "--version"],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    status_lines.append("✅ UPX: Available")
                else:
                    status_lines.append("❌ UPX: Not working properly")
            except FileNotFoundError:
                status_lines.append("❌ UPX: Not installed")
                status_lines.append("   Download from: https://upx.github.io/")
            except Exception as e:
                status_lines.append(f"❌ UPX: Error - {e}")

            status_lines.append("")
            status_lines.append("💡 TIP: Install missing tools to unlock additional features!")

            # Display results
            self.tool_status_text.insert("1.0", "\n".join(status_lines))

        except Exception as e:
            self.tool_status_text.insert("1.0", f"Error checking tool status: {e}")

    # Build methods
    def quick_build(self):
        """Quick build with current settings"""
        if not self.script_path.get():
            messagebox.showerror("Error", "Please select a Python script first!")
            self.build_status_label.configure(
                text="❌ No script selected",
                text_color=("red", "lightcoral")
            )
            return

        # Update status
        self.build_status_label.configure(
            text="🔄 Building...",
            text_color=("orange", "yellow")
        )

        self.show_output_tab()
        self.log_message("🚀 Starting quick build...")

        # Run build in separate thread to prevent UI freezing
        threading.Thread(target=self.run_build, daemon=True).start()
    
    def run_build(self):
        """Run the actual build process"""
        try:
            self.progress_bar.set(0.1)
            self.status_label.configure(text="Building...")

            # Create output directory if it doesn't exist
            output_dir = self.output_dir.get() or "dist"
            os.makedirs(output_dir, exist_ok=True)

            # Handle security wrapper
            script_to_build = self.script_path.get()
            if self.use_security_wrapper.get():
                script_to_build = self.create_security_wrapper()
                if not script_to_build:
                    return

            # Prepare PyInstaller command
            cmd = ["pyinstaller", "--clean"]

            if self.use_onefile.get():
                cmd.append("--onefile")

            if not self.use_console.get():
                cmd.append("--noconsole")

            exe_name = self.exe_name.get() or os.path.splitext(os.path.basename(self.script_path.get()))[0]
            cmd.extend(["--name", exe_name])

            if self.icon_path.get() and os.path.exists(self.icon_path.get()):
                cmd.extend(["--icon", self.icon_path.get()])

            # Auto-detect and add common hidden imports
            auto_imports = self.detect_required_imports(script_to_build)
            for module in auto_imports:
                cmd.extend(["--hidden-import", module])

            # Add user-specified hidden imports
            if self.hidden_imports.get():
                for module in self.hidden_imports.get().split(','):
                    module = module.strip()
                    if module:
                        cmd.extend(["--hidden-import", module])

            if self.exclude_modules.get():
                for module in self.exclude_modules.get().split(','):
                    module = module.strip()
                    if module:
                        cmd.extend(["--exclude-module", module])

            if self.add_data.get():
                for data_spec in self.add_data.get().split(';'):
                    data_spec = data_spec.strip()
                    if data_spec:
                        cmd.extend(["--add-data", data_spec])

            # Add Tkinter support if detected
            if self.uses_tkinter(script_to_build):
                cmd.extend(["--collect-all", "tkinter"])
                self.log_message("🎨 Tkinter detected - adding full Tkinter support")

            # Set output directory
            cmd.extend(["--distpath", output_dir])

            cmd.append(script_to_build)

            self.progress_bar.set(0.3)
            self.log_message(f"🔨 PyInstaller Command: {' '.join(cmd)}")

            # Run PyInstaller
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True,
                cwd=os.getcwd()
            )

            self.progress_bar.set(0.5)

            # Stream output
            for line in process.stdout:
                line = line.strip()
                if line:
                    self.log_message(line)

            process.wait()

            self.progress_bar.set(0.8)

            if process.returncode == 0:
                self.log_message("✅ PyInstaller build completed successfully!")

                # Check if executable was created
                exe_path = os.path.join(output_dir, f"{exe_name}.exe")
                if os.path.exists(exe_path):
                    self.log_message(f"✅ Executable created: {exe_path}")
                    file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                    self.log_message(f"📊 File size: {file_size:.2f} MB")
                else:
                    self.log_message(f"⚠️ Executable not found at expected location: {exe_path}")

                if self.create_zip.get():
                    self.create_zip_archive()

                self.status_label.configure(text="Build completed successfully")
                self.build_status_label.configure(
                    text="✅ Build completed successfully!",
                    text_color=("green", "lightgreen")
                )
            else:
                self.log_message("❌ PyInstaller build failed!")
                self.log_message("💡 Check the output above for specific error messages")
                self.status_label.configure(text="Build failed")
                self.build_status_label.configure(
                    text="❌ Build failed - check output",
                    text_color=("red", "lightcoral")
                )

            self.progress_bar.set(1.0)

        except Exception as e:
            self.log_message(f"❌ Build Error: {str(e)}")
            self.status_label.configure(text="Error occurred")
            self.progress_bar.set(0)
    
    def create_security_wrapper(self):
        """Create security wrapper for the executable"""
        try:
            self.log_message("🔒 Creating Security Wrapper™...")

            # Create temp directory for wrapper
            temp_dir = "temp_secure_build"
            os.makedirs(temp_dir, exist_ok=True)

            # Generate security key if not provided
            security_key = self.security_key.get() or f"secure_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Create wrapper script
            wrapper_content = f'''#!/usr/bin/env python3
"""
Security Wrapper™ for {os.path.basename(self.script_path.get())}
Generated by SecureBuilder v3.0 Professional
"""

import os
import sys
import hashlib
import uuid
from datetime import datetime

# Security configuration
SECURITY_KEY = "{security_key}"
APP_NAME = "{self.exe_name.get() or 'SecureApp'}"

def verify_security():
    """Verify security token"""
    try:
        # Generate expected token based on machine
        machine_id = str(uuid.getnode())
        expected_token = hashlib.sha256(f"{{machine_id}}-{{APP_NAME}}-{{SECURITY_KEY}}".encode()).hexdigest()[:16]

        # Check for token file
        token_file = f"{{APP_NAME}}_token.key"
        if os.path.exists(token_file):
            with open(token_file, 'r') as f:
                stored_token = f.read().strip()
            if stored_token == expected_token:
                return True

        # Create token file for first run
        with open(token_file, 'w') as f:
            f.write(expected_token)

        print(f"🔑 Security token created: {{token_file}}")
        return True

    except Exception as e:
        print(f"❌ Security verification failed: {{e}}")
        return False

def main():
    """Main wrapper function"""
    print(f"🛡️ SecureBuilder Security Wrapper™")
    print(f"App: {{APP_NAME}}")
    print(f"Time: {{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}}")
    print("=" * 50)

    if not verify_security():
        print("❌ Security verification failed!")
        input("Press Enter to exit...")
        sys.exit(1)

    print("✅ Security verification passed!")
    print("🚀 Starting application...")
    print("=" * 50)

    # Import and run the original script
    try:
'''

            # Read original script content
            with open(self.script_path.get(), 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Remove the if __name__ == "__main__": block and add it to wrapper
            lines = original_content.split('\n')
            filtered_lines = []
            skip_main = False

            for line in lines:
                if line.strip().startswith('if __name__ == "__main__":'):
                    skip_main = True
                    continue
                elif skip_main and (line.startswith('    ') or line.startswith('\t') or line.strip() == ''):
                    continue
                else:
                    skip_main = False
                    filtered_lines.append(line)

            # Add the filtered content to wrapper
            wrapper_content += '\n'.join(filtered_lines)
            wrapper_content += '''

        # Run the main function if it exists
        if 'main' in globals():
            main()
        else:
            print("⚠️ No main function found in script")

    except Exception as e:
        print(f"❌ Error running application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''

            # Write wrapper script
            wrapper_path = os.path.join(temp_dir, f"secure_{os.path.basename(self.script_path.get())}")
            with open(wrapper_path, 'w', encoding='utf-8') as f:
                f.write(wrapper_content)

            self.log_message(f"✅ Security Wrapper™ created: {wrapper_path}")
            return wrapper_path

        except Exception as e:
            self.log_message(f"❌ Security Wrapper™ creation failed: {str(e)}")
            return None

    def create_zip_archive(self):
        """Create ZIP archive of the built executable"""
        try:
            exe_name = self.exe_name.get() or "app"
            output_dir = self.output_dir.get() or "dist"
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            zip_name = f"{exe_name}_SecureBuilder_{timestamp}.zip"

            self.log_message(f"📦 Creating ZIP archive: {zip_name}")

            with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add executable
                exe_path = os.path.join(output_dir, f"{exe_name}.exe")
                if os.path.exists(exe_path):
                    zipf.write(exe_path, f"{exe_name}.exe")
                    self.log_message(f"📁 Added to ZIP: {exe_name}.exe")

                # Add any additional files from output directory
                if os.path.exists(output_dir):
                    for root, dirs, files in os.walk(output_dir):
                        for file in files:
                            if file.endswith('.exe') and file != f"{exe_name}.exe":
                                file_path = os.path.join(root, file)
                                arc_name = os.path.relpath(file_path, output_dir)
                                zipf.write(file_path, arc_name)
                                self.log_message(f"📁 Added to ZIP: {arc_name}")

                # Add README
                readme_content = f"""🛡️ SecureBuilder v3.0 Professional - Build Package
================================================================

Application: {exe_name}
Built: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Original Script: {os.path.basename(self.script_path.get())}

📁 Package Contents:
• {exe_name}.exe - Main executable
• README.txt - This file

🚀 Usage:
1. Extract all files to a folder
2. Run {exe_name}.exe
3. Enjoy your application!

Built with SecureBuilder v3.0 Professional
https://github.com/securebuilder
"""

                zipf.writestr("README.txt", readme_content)
                self.log_message("📁 Added to ZIP: README.txt")

            zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
            self.log_message(f"✅ ZIP archive created successfully!")
            self.log_message(f"📊 ZIP size: {zip_size:.2f} MB")
            self.log_message(f"📦 Location: {os.path.abspath(zip_name)}")

        except Exception as e:
            self.log_message(f"❌ ZIP creation failed: {str(e)}")
    
    # Utility methods
    def log_message(self, message):
        """Add message to output log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Update UI in main thread
        self.after(0, lambda: self.output_text.insert("end", formatted_message))
        self.after(0, lambda: self.output_text.see("end"))
    
    def clear_output(self):
        """Clear the output text area"""
        self.output_text.delete("1.0", "end")
    
    def change_theme(self, theme):
        """Change application theme"""
        if theme == "Dark":
            ctk.set_appearance_mode("dark")
        elif theme == "Light":
            ctk.set_appearance_mode("light")
        else:
            ctk.set_appearance_mode("system")
    
    def load_settings(self):
        """Load application settings"""
        try:
            settings_file = "securebuilder_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                if 'theme' in settings:
                    self.theme_menu.set(settings['theme'])
                    self.change_theme(settings['theme'])

                if 'last_output_dir' in settings:
                    self.output_dir.set(settings['last_output_dir'])

        except Exception as e:
            self.log_message(f"⚠️ Could not load settings: {str(e)}")

    def save_settings(self):
        """Save application settings"""
        try:
            settings = {
                'theme': self.theme_menu.get(),
                'last_output_dir': self.output_dir.get(),
                'window_geometry': self.geometry()
            }

            with open("securebuilder_settings.json", 'w') as f:
                json.dump(settings, f, indent=2)

        except Exception as e:
            self.log_message(f"⚠️ Could not save settings: {str(e)}")

    def save_project(self):
        """Save current project configuration"""
        if not self.script_path.get():
            messagebox.showwarning("Warning", "No script selected to save!")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Project",
            defaultextension=".sbproj",
            filetypes=[("SecureBuilder Project", "*.sbproj"), ("All files", "*.*")]
        )

        if filename:
            try:
                project_data = {
                    'script_path': self.script_path.get(),
                    'icon_path': self.icon_path.get(),
                    'output_dir': self.output_dir.get(),
                    'exe_name': self.exe_name.get(),
                    'use_onefile': self.use_onefile.get(),
                    'use_console': self.use_console.get(),
                    'use_pyarmor': self.use_pyarmor.get(),
                    'use_upx': self.use_upx.get(),
                    'create_zip': self.create_zip.get(),
                    'add_data': self.add_data.get(),
                    'hidden_imports': self.hidden_imports.get(),
                    'exclude_modules': self.exclude_modules.get(),
                    'created': datetime.now().isoformat(),
                    'version': '3.0'
                }

                with open(filename, 'w') as f:
                    json.dump(project_data, f, indent=2)

                self.current_project = filename
                self.log_message(f"💾 Project saved: {filename}")
                messagebox.showinfo("Success", "Project saved successfully!")

            except Exception as e:
                self.log_message(f"❌ Error saving project: {str(e)}")
                messagebox.showerror("Error", f"Could not save project: {str(e)}")

    def load_project(self):
        """Load project configuration"""
        filename = filedialog.askopenfilename(
            title="Load Project",
            filetypes=[("SecureBuilder Project", "*.sbproj"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    project_data = json.load(f)

                # Load project data
                self.script_path.set(project_data.get('script_path', ''))
                self.icon_path.set(project_data.get('icon_path', ''))
                self.output_dir.set(project_data.get('output_dir', 'dist'))
                self.exe_name.set(project_data.get('exe_name', ''))
                self.use_onefile.set(project_data.get('use_onefile', True))
                self.use_console.set(project_data.get('use_console', False))
                self.use_pyarmor.set(project_data.get('use_pyarmor', False))
                self.use_upx.set(project_data.get('use_upx', False))
                self.create_zip.set(project_data.get('create_zip', True))
                self.add_data.set(project_data.get('add_data', ''))
                self.hidden_imports.set(project_data.get('hidden_imports', ''))
                self.exclude_modules.set(project_data.get('exclude_modules', ''))

                self.current_project = filename
                self.log_message(f"📂 Project loaded: {filename}")
                messagebox.showinfo("Success", "Project loaded successfully!")

            except Exception as e:
                self.log_message(f"❌ Error loading project: {str(e)}")
                messagebox.showerror("Error", f"Could not load project: {str(e)}")

    def new_project(self):
        """Create new project (clear all settings)"""
        if messagebox.askyesno("New Project", "Clear all current settings and start a new project?"):
            # Reset all variables
            self.script_path.set('')
            self.icon_path.set('')
            self.output_dir.set('dist')
            self.exe_name.set('')
            self.use_onefile.set(True)
            self.use_console.set(False)
            self.use_pyarmor.set(False)
            self.use_upx.set(False)
            self.create_zip.set(True)
            self.add_data.set('')
            self.hidden_imports.set('')
            self.exclude_modules.set('')

            self.current_project = None
            self.clear_output()
            self.log_message("📄 New project created")

    def detect_required_imports(self, script_path):
        """Auto-detect required hidden imports"""
        imports = set()

        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Common modules that often need to be hidden imports
            common_modules = [
                'uuid', 'hashlib', 'hmac', 'secrets', 'base64',
                'urllib', 'http', 'email', 'xml', 'html',
                'sqlite3', 'csv', 'configparser', 'logging',
                'threading', 'multiprocessing', 'queue',
                'collections', 'itertools', 'functools',
                'pathlib', 'glob', 'shutil', 'tempfile'
            ]

            # Check for imports in the script
            for module in common_modules:
                if f'import {module}' in content or f'from {module}' in content:
                    imports.add(module)

            # Special cases
            if 'tkinter' in content.lower() or 'tk.' in content:
                imports.update(['tkinter', 'tkinter.filedialog', 'tkinter.messagebox'])

            if 'requests' in content:
                imports.update(['requests', 'urllib3', 'certifi'])

            if 'numpy' in content:
                imports.add('numpy')

            if 'pandas' in content:
                imports.update(['pandas', 'numpy'])

            if 'matplotlib' in content:
                imports.update(['matplotlib', 'numpy'])

            self.log_message(f"🔍 Auto-detected imports: {', '.join(imports) if imports else 'None'}")

        except Exception as e:
            self.log_message(f"⚠️ Could not analyze script for imports: {str(e)}")

        return list(imports)

    def uses_tkinter(self, script_path):
        """Check if script uses Tkinter"""
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()

            tkinter_indicators = [
                'import tkinter', 'from tkinter', 'import tk',
                'tk.', 'tkinter.', 'messagebox', 'filedialog',
                'ttk.', 'scrolledtext'
            ]

            return any(indicator in content for indicator in tkinter_indicators)

        except Exception:
            return False

    def on_closing(self):
        """Handle application closing"""
        self.save_settings()
        self.destroy()

def main():
    """Main application entry point"""
    try:
        app = SecureBuilderPro()
        app.protocol("WM_DELETE_WINDOW", app.on_closing)

        # Center window on screen
        app.update_idletasks()
        width = app.winfo_width()
        height = app.winfo_height()
        x = (app.winfo_screenwidth() // 2) - (width // 2)
        y = (app.winfo_screenheight() // 2) - (height // 2)
        app.geometry(f"{width}x{height}+{x}+{y}")

        app.mainloop()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start SecureBuilder: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
