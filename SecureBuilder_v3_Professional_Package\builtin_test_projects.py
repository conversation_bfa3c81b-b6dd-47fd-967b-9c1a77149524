#!/usr/bin/env python3
"""
Built-in Test Projects for SecureBuilder v3.0 Professional
==========================================================
Contains pre-built test applications that users can build to test SecureBuilder functionality.
"""

# Neon Alarm Clock Test Project
NEON_ALARM_CLOCK = '''#!/usr/bin/env python3
"""
🕐 Neon Alarm Clock - SecureBuilder Test Project
===============================================
A stylish neon-themed digital clock with alarm functionality.
Perfect for testing GUI builds with SecureBuilder.
"""

import tkinter as tk
from tkinter import messagebox, ttk
import time
import threading
import datetime
import winsound
import sys
import os

class NeonAlarmClock:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🕐 Neon Alarm Clock - SecureBuilder Test")
        self.root.geometry("600x400")
        self.root.configure(bg='#0a0a0a')
        self.root.resizable(False, False)
        
        # Variables
        self.alarm_time = tk.StringVar(value="07:00:00")
        self.alarm_enabled = tk.BooleanVar(value=False)
        self.current_time = tk.StringVar()
        self.alarm_thread = None
        self.running = True
        
        # Colors
        self.neon_green = '#00ff41'
        self.neon_blue = '#0080ff'
        self.neon_pink = '#ff0080'
        self.dark_bg = '#0a0a0a'
        self.panel_bg = '#1a1a1a'
        
        self.setup_ui()
        self.start_clock()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🕐 NEON ALARM CLOCK",
            font=("Courier New", 20, "bold"),
            fg=self.neon_green,
            bg=self.dark_bg
        )
        title_label.pack(pady=20)
        
        # Current time display
        self.time_label = tk.Label(
            self.root,
            textvariable=self.current_time,
            font=("Courier New", 36, "bold"),
            fg=self.neon_blue,
            bg=self.dark_bg
        )
        self.time_label.pack(pady=30)
        
        # Alarm panel
        alarm_frame = tk.Frame(self.root, bg=self.panel_bg, relief=tk.RAISED, bd=2)
        alarm_frame.pack(pady=20, padx=50, fill=tk.X)
        
        tk.Label(
            alarm_frame,
            text="⏰ ALARM SETTINGS",
            font=("Courier New", 14, "bold"),
            fg=self.neon_pink,
            bg=self.panel_bg
        ).pack(pady=10)
        
        # Alarm time input
        time_frame = tk.Frame(alarm_frame, bg=self.panel_bg)
        time_frame.pack(pady=10)
        
        tk.Label(
            time_frame,
            text="Alarm Time (HH:MM:SS):",
            font=("Courier New", 12),
            fg=self.neon_green,
            bg=self.panel_bg
        ).pack(side=tk.LEFT, padx=10)
        
        self.time_entry = tk.Entry(
            time_frame,
            textvariable=self.alarm_time,
            font=("Courier New", 12),
            bg=self.dark_bg,
            fg=self.neon_blue,
            insertbackground=self.neon_blue,
            width=15
        )
        self.time_entry.pack(side=tk.LEFT, padx=10)
        
        # Alarm controls
        control_frame = tk.Frame(alarm_frame, bg=self.panel_bg)
        control_frame.pack(pady=15)
        
        self.alarm_checkbox = tk.Checkbutton(
            control_frame,
            text="Enable Alarm",
            variable=self.alarm_enabled,
            font=("Courier New", 12),
            fg=self.neon_green,
            bg=self.panel_bg,
            selectcolor=self.dark_bg,
            activebackground=self.panel_bg,
            activeforeground=self.neon_green,
            command=self.toggle_alarm
        )
        self.alarm_checkbox.pack(side=tk.LEFT, padx=20)
        
        test_btn = tk.Button(
            control_frame,
            text="🔔 Test Alarm",
            command=self.test_alarm,
            font=("Courier New", 10, "bold"),
            bg=self.neon_pink,
            fg=self.dark_bg,
            activebackground=self.neon_green,
            activeforeground=self.dark_bg,
            relief=tk.FLAT,
            padx=20
        )
        test_btn.pack(side=tk.LEFT, padx=20)
        
        # Status
        self.status_label = tk.Label(
            self.root,
            text="🟢 Clock Running - Built with SecureBuilder v3.0",
            font=("Courier New", 10),
            fg=self.neon_green,
            bg=self.dark_bg
        )
        self.status_label.pack(side=tk.BOTTOM, pady=10)
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def start_clock(self):
        """Start the clock update thread"""
        self.update_time()
        
    def update_time(self):
        """Update the current time display"""
        if self.running:
            now = datetime.datetime.now()
            current = now.strftime("%H:%M:%S")
            date_str = now.strftime("%Y-%m-%d")
            
            self.current_time.set(f"{current}\\n{date_str}")
            
            # Check alarm
            if self.alarm_enabled.get() and current == self.alarm_time.get():
                self.trigger_alarm()
            
            # Schedule next update
            self.root.after(1000, self.update_time)
    
    def toggle_alarm(self):
        """Toggle alarm on/off"""
        if self.alarm_enabled.get():
            self.status_label.config(
                text=f"⏰ Alarm set for {self.alarm_time.get()}",
                fg=self.neon_pink
            )
        else:
            self.status_label.config(
                text="🟢 Clock Running - Built with SecureBuilder v3.0",
                fg=self.neon_green
            )
    
    def test_alarm(self):
        """Test the alarm sound"""
        self.trigger_alarm(test=True)
    
    def trigger_alarm(self, test=False):
        """Trigger the alarm"""
        try:
            # Flash the display
            original_bg = self.time_label.cget('bg')
            for _ in range(6):
                self.time_label.config(bg=self.neon_pink)
                self.root.update()
                time.sleep(0.2)
                self.time_label.config(bg=original_bg)
                self.root.update()
                time.sleep(0.2)
            
            # Play alarm sound
            try:
                winsound.Beep(1000, 1000)  # 1000Hz for 1 second
            except:
                print("\\a")  # Fallback beep
            
            # Show message
            message = "🔔 Test Alarm!" if test else "⏰ ALARM! Wake up!"
            messagebox.showinfo("Alarm", message)
            
            if not test:
                self.alarm_enabled.set(False)
                self.toggle_alarm()
                
        except Exception as e:
            messagebox.showerror("Error", f"Alarm error: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("🕐 Starting Neon Alarm Clock...")
    print("Built with SecureBuilder v3.0 Professional")
    print("=" * 50)
    
    try:
        app = NeonAlarmClock()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''

# System Hardware Monitor Test Project
SYSTEM_HW_MONITOR = '''#!/usr/bin/env python3
"""
🖥️ System Hardware Monitor - SecureBuilder Test Project
======================================================
A comprehensive system hardware monitoring tool with real-time updates.
Perfect for testing advanced builds with SecureBuilder.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import psutil
import platform
import threading
import time
import datetime
import sys
import os

class SystemHWMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🖥️ System Hardware Monitor - SecureBuilder Test")
        self.root.geometry("800x600")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.running = True
        self.update_interval = 2000  # 2 seconds
        
        # Colors
        self.bg_color = '#2b2b2b'
        self.panel_color = '#3b3b3b'
        self.text_color = '#ffffff'
        self.accent_color = '#4CAF50'
        self.warning_color = '#FF9800'
        self.critical_color = '#F44336'
        
        self.setup_ui()
        self.start_monitoring()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title_frame = tk.Frame(self.root, bg=self.bg_color)
        title_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(
            title_frame,
            text="🖥️ SYSTEM HARDWARE MONITOR",
            font=("Arial", 18, "bold"),
            fg=self.accent_color,
            bg=self.bg_color
        ).pack()
        
        tk.Label(
            title_frame,
            text="Built with SecureBuilder v3.0 Professional",
            font=("Arial", 10),
            fg=self.text_color,
            bg=self.bg_color
        ).pack()
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # System Info Tab
        self.create_system_tab()
        
        # CPU Tab
        self.create_cpu_tab()
        
        # Memory Tab
        self.create_memory_tab()
        
        # Disk Tab
        self.create_disk_tab()
        
        # Network Tab
        self.create_network_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("🟢 Monitoring Active")
        
        status_bar = tk.Label(
            self.root,
            textvariable=self.status_var,
            font=("Arial", 10),
            fg=self.accent_color,
            bg=self.bg_color,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_system_tab(self):
        """Create system information tab"""
        frame = tk.Frame(self.notebook, bg=self.panel_color)
        self.notebook.add(frame, text="💻 System Info")
        
        # System info text
        self.system_text = tk.Text(
            frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 10),
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.system_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.update_system_info()
        
    def create_cpu_tab(self):
        """Create CPU monitoring tab"""
        frame = tk.Frame(self.notebook, bg=self.panel_color)
        self.notebook.add(frame, text="🔥 CPU")
        
        # CPU usage
        cpu_frame = tk.LabelFrame(
            frame,
            text="CPU Usage",
            bg=self.panel_color,
            fg=self.text_color,
            font=("Arial", 12, "bold")
        )
        cpu_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.cpu_var = tk.StringVar()
        self.cpu_label = tk.Label(
            cpu_frame,
            textvariable=self.cpu_var,
            font=("Courier New", 14, "bold"),
            bg=self.panel_color,
            fg=self.accent_color
        )
        self.cpu_label.pack(pady=10)
        
        # CPU cores
        cores_frame = tk.LabelFrame(
            frame,
            text="CPU Cores",
            bg=self.panel_color,
            fg=self.text_color,
            font=("Arial", 12, "bold")
        )
        cores_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.cores_text = tk.Text(
            cores_frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 9),
            height=8,
            state=tk.DISABLED
        )
        self.cores_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def create_memory_tab(self):
        """Create memory monitoring tab"""
        frame = tk.Frame(self.notebook, bg=self.panel_color)
        self.notebook.add(frame, text="🧠 Memory")
        
        # RAM usage
        ram_frame = tk.LabelFrame(
            frame,
            text="RAM Usage",
            bg=self.panel_color,
            fg=self.text_color,
            font=("Arial", 12, "bold")
        )
        ram_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.ram_text = tk.Text(
            ram_frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 10),
            height=6,
            state=tk.DISABLED
        )
        self.ram_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Swap usage
        swap_frame = tk.LabelFrame(
            frame,
            text="Swap Usage",
            bg=self.panel_color,
            fg=self.text_color,
            font=("Arial", 12, "bold")
        )
        swap_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.swap_text = tk.Text(
            swap_frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 10),
            height=4,
            state=tk.DISABLED
        )
        self.swap_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def create_disk_tab(self):
        """Create disk monitoring tab"""
        frame = tk.Frame(self.notebook, bg=self.panel_color)
        self.notebook.add(frame, text="💾 Disk")
        
        self.disk_text = tk.Text(
            frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 9),
            wrap=tk.NONE,
            state=tk.DISABLED
        )
        
        # Add scrollbars
        v_scroll = tk.Scrollbar(frame, orient=tk.VERTICAL, command=self.disk_text.yview)
        h_scroll = tk.Scrollbar(frame, orient=tk.HORIZONTAL, command=self.disk_text.xview)
        self.disk_text.configure(yscrollcommand=v_scroll.set, xscrollcommand=h_scroll.set)
        
        self.disk_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        v_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        h_scroll.pack(side=tk.BOTTOM, fill=tk.X, padx=10)
        
    def create_network_tab(self):
        """Create network monitoring tab"""
        frame = tk.Frame(self.notebook, bg=self.panel_color)
        self.notebook.add(frame, text="🌐 Network")
        
        self.network_text = tk.Text(
            frame,
            bg=self.bg_color,
            fg=self.text_color,
            font=("Courier New", 10),
            state=tk.DISABLED
        )
        self.network_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def update_system_info(self):
        """Update system information"""
        try:
            info = []
            info.append("=" * 60)
            info.append("SYSTEM INFORMATION")
            info.append("=" * 60)
            info.append(f"System: {platform.system()} {platform.release()}")
            info.append(f"Version: {platform.version()}")
            info.append(f"Architecture: {platform.architecture()[0]}")
            info.append(f"Machine: {platform.machine()}")
            info.append(f"Processor: {platform.processor()}")
            info.append(f"Hostname: {platform.node()}")
            info.append(f"Python: {platform.python_version()}")
            info.append("")
            info.append(f"Boot Time: {datetime.datetime.fromtimestamp(psutil.boot_time())}")
            info.append(f"Current Time: {datetime.datetime.now()}")
            info.append("")
            info.append("CPU Information:")
            info.append(f"  Physical Cores: {psutil.cpu_count(logical=False)}")
            info.append(f"  Logical Cores: {psutil.cpu_count(logical=True)}")
            
            try:
                freq = psutil.cpu_freq()
                if freq:
                    info.append(f"  Max Frequency: {freq.max:.2f} MHz")
                    info.append(f"  Current Frequency: {freq.current:.2f} MHz")
            except:
                info.append("  Frequency: Not available")
            
            info.append("")
            info.append("Built with SecureBuilder v3.0 Professional")
            info.append("Test application for executable building")
            
            self.system_text.config(state=tk.NORMAL)
            self.system_text.delete(1.0, tk.END)
            self.system_text.insert(1.0, "\\n".join(info))
            self.system_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.system_text.config(state=tk.NORMAL)
            self.system_text.delete(1.0, tk.END)
            self.system_text.insert(1.0, f"Error getting system info: {e}")
            self.system_text.config(state=tk.DISABLED)
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        self.update_data()
        
    def update_data(self):
        """Update all monitoring data"""
        if self.running:
            try:
                self.update_cpu_data()
                self.update_memory_data()
                self.update_disk_data()
                self.update_network_data()
                
                # Update status
                self.status_var.set(f"🟢 Last Update: {datetime.datetime.now().strftime('%H:%M:%S')}")
                
            except Exception as e:
                self.status_var.set(f"❌ Error: {str(e)}")
            
            # Schedule next update
            self.root.after(self.update_interval, self.update_data)
    
    def update_cpu_data(self):
        """Update CPU data"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_var.set(f"CPU Usage: {cpu_percent:.1f}%")
            
            # Update CPU color based on usage
            if cpu_percent < 50:
                color = self.accent_color
            elif cpu_percent < 80:
                color = self.warning_color
            else:
                color = self.critical_color
            
            self.cpu_label.config(fg=color)
            
            # Update per-core data
            core_data = []
            core_data.append("Per-Core CPU Usage:")
            core_data.append("-" * 30)
            
            for i, percent in enumerate(psutil.cpu_percent(percpu=True)):
                core_data.append(f"Core {i:2d}: {percent:5.1f}%")
            
            self.cores_text.config(state=tk.NORMAL)
            self.cores_text.delete(1.0, tk.END)
            self.cores_text.insert(1.0, "\\n".join(core_data))
            self.cores_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.cpu_var.set(f"CPU Error: {e}")
    
    def update_memory_data(self):
        """Update memory data"""
        try:
            # RAM
            ram = psutil.virtual_memory()
            ram_data = []
            ram_data.append(f"Total RAM: {ram.total / (1024**3):.2f} GB")
            ram_data.append(f"Available: {ram.available / (1024**3):.2f} GB")
            ram_data.append(f"Used: {ram.used / (1024**3):.2f} GB")
            ram_data.append(f"Percentage: {ram.percent:.1f}%")
            ram_data.append(f"Free: {ram.free / (1024**3):.2f} GB")
            
            self.ram_text.config(state=tk.NORMAL)
            self.ram_text.delete(1.0, tk.END)
            self.ram_text.insert(1.0, "\\n".join(ram_data))
            self.ram_text.config(state=tk.DISABLED)
            
            # Swap
            swap = psutil.swap_memory()
            swap_data = []
            swap_data.append(f"Total Swap: {swap.total / (1024**3):.2f} GB")
            swap_data.append(f"Used: {swap.used / (1024**3):.2f} GB")
            swap_data.append(f"Free: {swap.free / (1024**3):.2f} GB")
            swap_data.append(f"Percentage: {swap.percent:.1f}%")
            
            self.swap_text.config(state=tk.NORMAL)
            self.swap_text.delete(1.0, tk.END)
            self.swap_text.insert(1.0, "\\n".join(swap_data))
            self.swap_text.config(state=tk.DISABLED)
            
        except Exception as e:
            pass
    
    def update_disk_data(self):
        """Update disk data"""
        try:
            disk_data = []
            disk_data.append("DISK USAGE")
            disk_data.append("=" * 80)
            disk_data.append(f"{'Device':<15} {'Total':<10} {'Used':<10} {'Free':<10} {'%':<6} {'Type':<10} {'Mount'}")
            disk_data.append("-" * 80)
            
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_data.append(
                        f"{partition.device:<15} "
                        f"{usage.total/(1024**3):<10.1f} "
                        f"{usage.used/(1024**3):<10.1f} "
                        f"{usage.free/(1024**3):<10.1f} "
                        f"{(usage.used/usage.total)*100:<6.1f} "
                        f"{partition.fstype:<10} "
                        f"{partition.mountpoint}"
                    )
                except PermissionError:
                    disk_data.append(f"{partition.device:<15} {'Access Denied'}")
            
            self.disk_text.config(state=tk.NORMAL)
            self.disk_text.delete(1.0, tk.END)
            self.disk_text.insert(1.0, "\\n".join(disk_data))
            self.disk_text.config(state=tk.DISABLED)
            
        except Exception as e:
            pass
    
    def update_network_data(self):
        """Update network data"""
        try:
            net_data = []
            net_data.append("NETWORK STATISTICS")
            net_data.append("=" * 50)
            
            net_io = psutil.net_io_counters()
            net_data.append(f"Bytes Sent: {net_io.bytes_sent / (1024**2):.2f} MB")
            net_data.append(f"Bytes Received: {net_io.bytes_recv / (1024**2):.2f} MB")
            net_data.append(f"Packets Sent: {net_io.packets_sent:,}")
            net_data.append(f"Packets Received: {net_io.packets_recv:,}")
            net_data.append("")
            
            net_data.append("Network Interfaces:")
            net_data.append("-" * 30)
            
            for interface, addrs in psutil.net_if_addrs().items():
                net_data.append(f"\\n{interface}:")
                for addr in addrs:
                    if addr.family.name == 'AF_INET':
                        net_data.append(f"  IPv4: {addr.address}")
                    elif addr.family.name == 'AF_INET6':
                        net_data.append(f"  IPv6: {addr.address}")
            
            self.network_text.config(state=tk.NORMAL)
            self.network_text.delete(1.0, tk.END)
            self.network_text.insert(1.0, "\\n".join(net_data))
            self.network_text.config(state=tk.DISABLED)
            
        except Exception as e:
            pass
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("🖥️ Starting System Hardware Monitor...")
    print("Built with SecureBuilder v3.0 Professional")
    print("=" * 50)
    
    try:
        app = SystemHWMonitor()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''

# Test project metadata
TEST_PROJECTS = {
    "neon_alarm_clock": {
        "name": "🕐 Neon Alarm Clock",
        "description": "Stylish neon-themed digital clock with alarm functionality",
        "filename": "neon_alarm_clock.py",
        "code": NEON_ALARM_CLOCK,
        "requirements": ["tkinter", "winsound", "datetime", "threading"],
        "build_options": {
            "use_onefile": True,
            "use_console": False,
            "hidden_imports": ["tkinter.messagebox", "tkinter.filedialog", "winsound"],
            "icon_suggestion": "clock or alarm icon"
        }
    },
    "system_hw_monitor": {
        "name": "🖥️ System Hardware Monitor",
        "description": "Comprehensive system hardware monitoring tool with real-time updates",
        "filename": "system_hw_monitor.py",
        "code": SYSTEM_HW_MONITOR,
        "requirements": ["tkinter", "psutil", "platform", "threading"],
        "build_options": {
            "use_onefile": True,
            "use_console": False,
            "hidden_imports": ["psutil", "tkinter.ttk", "platform", "threading"],
            "icon_suggestion": "computer or monitor icon"
        }
    }
}
