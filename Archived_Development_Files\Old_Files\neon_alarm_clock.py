#!/usr/bin/env python3
"""
🕐 Neon Alarm Clock - SecureBuilder Test Project
===============================================
A stylish neon-themed digital clock with alarm functionality.
Perfect for testing GUI builds with SecureBuilder.
"""

import tkinter as tk
from tkinter import messagebox, ttk
import time
import threading
import datetime
import winsound
import sys
import os

class NeonAlarmClock:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🕐 Neon Alarm Clock - SecureBuilder Test")
        self.root.geometry("600x400")
        self.root.configure(bg='#0a0a0a')
        self.root.resizable(False, False)
        
        # Variables
        self.alarm_time = tk.StringVar(value="07:00:00")
        self.alarm_enabled = tk.BooleanVar(value=False)
        self.current_time = tk.StringVar()
        self.alarm_thread = None
        self.running = True
        
        # Colors
        self.neon_green = '#00ff41'
        self.neon_blue = '#0080ff'
        self.neon_pink = '#ff0080'
        self.dark_bg = '#0a0a0a'
        self.panel_bg = '#1a1a1a'
        
        self.setup_ui()
        self.start_clock()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🕐 NEON ALARM CLOCK",
            font=("Courier New", 20, "bold"),
            fg=self.neon_green,
            bg=self.dark_bg
        )
        title_label.pack(pady=20)
        
        # Current time display
        self.time_label = tk.Label(
            self.root,
            textvariable=self.current_time,
            font=("Courier New", 36, "bold"),
            fg=self.neon_blue,
            bg=self.dark_bg
        )
        self.time_label.pack(pady=30)
        
        # Alarm panel
        alarm_frame = tk.Frame(self.root, bg=self.panel_bg, relief=tk.RAISED, bd=2)
        alarm_frame.pack(pady=20, padx=50, fill=tk.X)
        
        tk.Label(
            alarm_frame,
            text="⏰ ALARM SETTINGS",
            font=("Courier New", 14, "bold"),
            fg=self.neon_pink,
            bg=self.panel_bg
        ).pack(pady=10)
        
        # Alarm time input
        time_frame = tk.Frame(alarm_frame, bg=self.panel_bg)
        time_frame.pack(pady=10)
        
        tk.Label(
            time_frame,
            text="Alarm Time (HH:MM:SS):",
            font=("Courier New", 12),
            fg=self.neon_green,
            bg=self.panel_bg
        ).pack(side=tk.LEFT, padx=10)
        
        self.time_entry = tk.Entry(
            time_frame,
            textvariable=self.alarm_time,
            font=("Courier New", 12),
            bg=self.dark_bg,
            fg=self.neon_blue,
            insertbackground=self.neon_blue,
            width=15
        )
        self.time_entry.pack(side=tk.LEFT, padx=10)
        
        # Alarm controls
        control_frame = tk.Frame(alarm_frame, bg=self.panel_bg)
        control_frame.pack(pady=15)
        
        self.alarm_checkbox = tk.Checkbutton(
            control_frame,
            text="Enable Alarm",
            variable=self.alarm_enabled,
            font=("Courier New", 12),
            fg=self.neon_green,
            bg=self.panel_bg,
            selectcolor=self.dark_bg,
            activebackground=self.panel_bg,
            activeforeground=self.neon_green,
            command=self.toggle_alarm
        )
        self.alarm_checkbox.pack(side=tk.LEFT, padx=20)
        
        test_btn = tk.Button(
            control_frame,
            text="🔔 Test Alarm",
            command=self.test_alarm,
            font=("Courier New", 10, "bold"),
            bg=self.neon_pink,
            fg=self.dark_bg,
            activebackground=self.neon_green,
            activeforeground=self.dark_bg,
            relief=tk.FLAT,
            padx=20
        )
        test_btn.pack(side=tk.LEFT, padx=20)
        
        # Status
        self.status_label = tk.Label(
            self.root,
            text="🟢 Clock Running - Built with SecureBuilder v3.0",
            font=("Courier New", 10),
            fg=self.neon_green,
            bg=self.dark_bg
        )
        self.status_label.pack(side=tk.BOTTOM, pady=10)
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def start_clock(self):
        """Start the clock update thread"""
        self.update_time()
        
    def update_time(self):
        """Update the current time display"""
        if self.running:
            now = datetime.datetime.now()
            current = now.strftime("%H:%M:%S")
            date_str = now.strftime("%Y-%m-%d")
            
            self.current_time.set(f"{current}\n{date_str}")
            
            # Check alarm
            if self.alarm_enabled.get() and current == self.alarm_time.get():
                self.trigger_alarm()
            
            # Schedule next update
            self.root.after(1000, self.update_time)
    
    def toggle_alarm(self):
        """Toggle alarm on/off"""
        if self.alarm_enabled.get():
            self.status_label.config(
                text=f"⏰ Alarm set for {self.alarm_time.get()}",
                fg=self.neon_pink
            )
        else:
            self.status_label.config(
                text="🟢 Clock Running - Built with SecureBuilder v3.0",
                fg=self.neon_green
            )
    
    def test_alarm(self):
        """Test the alarm sound"""
        self.trigger_alarm(test=True)
    
    def trigger_alarm(self, test=False):
        """Trigger the alarm"""
        try:
            # Flash the display
            original_bg = self.time_label.cget('bg')
            for _ in range(6):
                self.time_label.config(bg=self.neon_pink)
                self.root.update()
                time.sleep(0.2)
                self.time_label.config(bg=original_bg)
                self.root.update()
                time.sleep(0.2)
            
            # Play alarm sound
            try:
                winsound.Beep(1000, 1000)  # 1000Hz for 1 second
            except:
                print("\a")  # Fallback beep
            
            # Show message
            message = "🔔 Test Alarm!" if test else "⏰ ALARM! Wake up!"
            messagebox.showinfo("Alarm", message)
            
            if not test:
                self.alarm_enabled.set(False)
                self.toggle_alarm()
                
        except Exception as e:
            messagebox.showerror("Error", f"Alarm error: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        self.root.destroy()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main entry point"""
    print("🕐 Starting Neon Alarm Clock...")
    print("Built with SecureBuilder v3.0 Professional")
    print("=" * 50)
    
    try:
        app = NeonAlarmClock()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
