import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { logger, LogLevel } from '@/utils/logger';

interface DebugPanelProps {
  open: boolean;
  onClose: () => void;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ open, onClose }) => {
  const [logs, setLogs] = useState(logger.getLogs());
  const [filter, setFilter] = useState<LogLevel | 'ALL'>('ALL');
  const [search, setSearch] = useState('');

  useEffect(() => {
    if (open) {
      // Refresh logs when panel opens
      setLogs([...logger.getStoredLogs(), ...logger.getLogs()]);
    }
  }, [open]);

  const filteredLogs = logs.filter(log => {
    const matchesLevel = filter === 'ALL' || log.level === filter;
    const matchesSearch = search === '' || 
      log.message.toLowerCase().includes(search.toLowerCase()) ||
      log.component?.toLowerCase().includes(search.toLowerCase());
    return matchesLevel && matchesSearch;
  }).slice(-100); // Show last 100 logs

  const handleExportLogs = () => {
    try {
      const logText = logger.exportLogs();
      const blob = new Blob([logText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `securebuilder-debug-${new Date().toISOString().slice(0, 19)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      logger.error('Failed to export debug logs', error, 'DebugPanel');
    }
  };

  const handleClearLogs = () => {
    logger.clearLogs();
    setLogs([]);
  };

  const handleRefresh = () => {
    setLogs([...logger.getStoredLogs(), ...logger.getLogs()]);
  };

  const getLogColor = (level: LogLevel) => {
    switch (level) {
      case 'DEBUG': return 'default';
      case 'INFO': return 'primary';
      case 'WARN': return 'warning';
      case 'ERROR': return 'error';
      default: return 'default';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">🐛 Debug Panel</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh logs">
              <IconButton onClick={handleRefresh} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export logs">
              <IconButton onClick={handleExportLogs} size="small">
                <DownloadIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Clear logs">
              <IconButton onClick={handleClearLogs} size="small">
                <DeleteIcon />
              </IconButton>
            </Tooltip>
            <IconButton onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
          <TextField
            label="Search logs"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            size="small"
            sx={{ flexGrow: 1 }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Level</InputLabel>
            <Select
              value={filter}
              label="Level"
              onChange={(e) => setFilter(e.target.value as LogLevel | 'ALL')}
            >
              <MenuItem value="ALL">All</MenuItem>
              <MenuItem value="DEBUG">Debug</MenuItem>
              <MenuItem value="INFO">Info</MenuItem>
              <MenuItem value="WARN">Warning</MenuItem>
              <MenuItem value="ERROR">Error</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Typography variant="body2" color="text.secondary" gutterBottom>
          Showing {filteredLogs.length} of {logs.length} log entries
        </Typography>

        <Box
          sx={{
            height: 400,
            overflow: 'auto',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <List dense>
            {filteredLogs.length === 0 ? (
              <ListItem>
                <ListItemText
                  primary="No logs found"
                  secondary="Try adjusting your search or filter criteria"
                />
              </ListItem>
            ) : (
              filteredLogs.map((log, index) => (
                <ListItem key={index} divider>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                          {formatTimestamp(log.timestamp)}
                        </Typography>
                        <Chip
                          label={log.level}
                          color={getLogColor(log.level)}
                          size="small"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                        {log.component && (
                          <Chip
                            label={log.component}
                            variant="outlined"
                            size="small"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                          {log.message}
                        </Typography>
                        {log.data && (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              fontFamily: 'monospace',
                              fontSize: '0.75rem',
                              mt: 0.5,
                              backgroundColor: 'action.hover',
                              p: 0.5,
                              borderRadius: 0.5,
                              overflow: 'auto',
                              maxHeight: 100,
                            }}
                          >
                            {JSON.stringify(log.data, null, 2)}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
              ))
            )}
          </List>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>System Info:</strong>
            <br />
            Platform: {window.electronAPI?.platform || 'Browser'}
            <br />
            User Agent: {navigator.userAgent}
            <br />
            Electron API: {window.electronAPI ? 'Available' : 'Not Available'}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClearLogs} color="warning">
          Clear All Logs
        </Button>
        <Button onClick={handleExportLogs} variant="outlined">
          Export Logs
        </Button>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DebugPanel;
