@echo off
echo Extracting all ZIP files in current directory...
echo.

REM Loop through all ZIP files in the current directory
for %%f in (*.zip) do (
    echo Extracting: %%f
    
    REM Extract to current directory (where the bat file is)
    powershell -command "Expand-Archive -Path '%%f' -DestinationPath '.' -Force"
    
    echo Completed: %%f
    echo.
)

echo.
echo All ZIP files have been extracted to the current directory.
echo Press any key to continue...
pause >nul
