import React, { useState } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Save as SaveIcon,
  FolderOpen as OpenIcon,
  ExpandMore as ExpandMoreIcon,
  Rule as RuleIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { 
  applyPatch, 
  undoPatch, 
  redoPatch, 
  loadConfig, 
  toggleFishRule,
  loadFishRules 
} from '@/store/slices/configSlice';
import { addNotification } from '@/store/slices/uiSlice';

const ConfigManager: React.FC = () => {
  const dispatch = useDispatch();
  const { 
    currentConfig, 
    patchHistory, 
    undoStack, 
    redoStack, 
    validationErrors,
    fishRules 
  } = useSelector((state: RootState) => state.config);
  
  const [patchDialogOpen, setPatchDialogOpen] = useState(false);
  const [newPatchData, setNewPatchData] = useState({
    target: '',
    field: '',
    value: '',
    reason: '',
  });

  // Initialize sample F.I.S.H. rules
  React.useEffect(() => {
    if (fishRules.length === 0) {
      dispatch(loadFishRules([
        {
          id: 'rule_1',
          name: 'Price Range Validation',
          description: 'Ensures all item prices are within acceptable ranges',
          enabled: true,
          conditions: { field: 'base_price', operator: 'between', min: 1, max: 10000 },
          actions: { type: 'validate', message: 'Price must be between 1 and 10000' },
        },
        {
          id: 'rule_2',
          name: 'Seasonal Markup',
          description: 'Applies seasonal price adjustments',
          enabled: false,
          conditions: { category: 'food', season: 'winter' },
          actions: { type: 'multiply', factor: 1.2 },
        },
        {
          id: 'rule_3',
          name: 'Rarity Enforcement',
          description: 'Ensures rare items have appropriate spawn rates',
          enabled: true,
          conditions: { rarity: 'legendary' },
          actions: { type: 'limit', field: 'spawn_rate', max: 0.01 },
        },
      ]));
    }
  }, [fishRules.length, dispatch]);

  const handleLoadConfig = async () => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: ['openFile'],
          filters: [
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'Config Files', extensions: ['cfg', 'ini'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          // TODO: Load actual config file
          const sampleConfig = {
            id: 'config_1',
            name: 'SCUM Economy Config',
            version: '1.0.0',
            data: {
              traders: {
                Trader_A: {
                  base_price: 1500,
                  markup: 0.2,
                  category: 'weapons',
                },
                Trader_B: {
                  base_price: 800,
                  markup: 0.15,
                  category: 'food',
                },
              },
              global_settings: {
                economy_multiplier: 1.0,
                inflation_rate: 0.02,
              },
            },
            lastModified: new Date().toISOString(),
          };
          
          dispatch(loadConfig(sampleConfig));
          dispatch(addNotification({
            type: 'success',
            title: 'Config Loaded',
            message: 'Economic configuration loaded successfully.',
          }));
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to load configuration file.',
        }));
      }
    }
  };

  const handleApplyPatch = () => {
    if (!newPatchData.target || !newPatchData.field || !newPatchData.reason) {
      dispatch(addNotification({
        type: 'error',
        title: 'Invalid Patch',
        message: 'Please fill in all required fields.',
      }));
      return;
    }

    const currentValue = getCurrentValue(newPatchData.target, newPatchData.field);
    
    dispatch(applyPatch({
      target: newPatchData.target,
      field: newPatchData.field,
      before: currentValue,
      after: parseValue(newPatchData.value),
      user: 'dev_user', // TODO: Get from user settings
      reason: newPatchData.reason,
    }));

    setPatchDialogOpen(false);
    setNewPatchData({ target: '', field: '', value: '', reason: '' });
    
    dispatch(addNotification({
      type: 'success',
      title: 'Patch Applied',
      message: `Successfully updated ${newPatchData.field} for ${newPatchData.target}`,
    }));
  };

  const getCurrentValue = (target: string, field: string) => {
    if (!currentConfig) return null;
    
    const keys = field.split('.');
    let value: any = currentConfig.data[target];
    
    for (const key of keys) {
      if (value && typeof value === 'object') {
        value = value[key];
      } else {
        return null;
      }
    }
    
    return value;
  };

  const parseValue = (value: string) => {
    // Try to parse as number
    const num = Number(value);
    if (!isNaN(num)) return num;
    
    // Try to parse as boolean
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // Return as string
    return value;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Configuration Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<OpenIcon />}
            onClick={handleLoadConfig}
          >
            Load Config
          </Button>
          <Button
            variant="outlined"
            startIcon={<UndoIcon />}
            onClick={() => dispatch(undoPatch())}
            disabled={undoStack.length === 0}
          >
            Undo
          </Button>
          <Button
            variant="outlined"
            startIcon={<RedoIcon />}
            onClick={() => dispatch(redoPatch())}
            disabled={redoStack.length === 0}
          >
            Redo
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setPatchDialogOpen(true)}
            disabled={!currentConfig}
          >
            Apply Patch
          </Button>
        </Box>
      </Box>

      {validationErrors.length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Validation Errors:
          </Typography>
          {validationErrors.map((error, index) => (
            <Typography key={index} variant="body2">
              • {error.field}: {error.message}
            </Typography>
          ))}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Current Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Configuration
              </Typography>
              {currentConfig ? (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    {currentConfig.name} (v{currentConfig.version})
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Last modified: {formatDate(currentConfig.lastModified)}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <pre style={{ 
                      fontSize: '0.8rem', 
                      backgroundColor: '#2a2a2a', 
                      padding: '1rem', 
                      borderRadius: '4px',
                      overflow: 'auto',
                      maxHeight: '300px',
                    }}>
                      {JSON.stringify(currentConfig.data, null, 2)}
                    </pre>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No configuration loaded. Load a config file to get started.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* F.I.S.H. Rules */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                F.I.S.H. Rules Engine
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Flexible, Intelligent, Safe, Harmonious rule system
              </Typography>
              
              <List dense>
                {fishRules.map((rule) => (
                  <ListItem key={rule.id}>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <RuleIcon fontSize="small" />
                          <Typography variant="body2">
                            {rule.name}
                          </Typography>
                          <Chip
                            label={rule.enabled ? 'Active' : 'Inactive'}
                            color={rule.enabled ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={rule.description}
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        edge="end"
                        checked={rule.enabled}
                        onChange={() => dispatch(toggleFishRule(rule.id))}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Patch History */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Patch History
              </Typography>
              {patchHistory.length > 0 ? (
                <List dense>
                  {patchHistory.slice(-10).reverse().map((patch) => (
                    <ListItem key={patch.id}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <HistoryIcon fontSize="small" />
                            <Typography variant="body2">
                              {patch.target}.{patch.field}: {JSON.stringify(patch.before)} → {JSON.stringify(patch.after)}
                            </Typography>
                          </Box>
                        }
                        secondary={`${patch.reason} - ${patch.user} at ${formatDate(patch.timestamp)}`}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No patches applied yet.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Apply Patch Dialog */}
      <Dialog open={patchDialogOpen} onClose={() => setPatchDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Apply Configuration Patch</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              fullWidth
              label="Target"
              value={newPatchData.target}
              onChange={(e) => setNewPatchData({ ...newPatchData, target: e.target.value })}
              placeholder="e.g., Trader_A"
            />
            <TextField
              fullWidth
              label="Field"
              value={newPatchData.field}
              onChange={(e) => setNewPatchData({ ...newPatchData, field: e.target.value })}
              placeholder="e.g., base_price"
            />
            <TextField
              fullWidth
              label="New Value"
              value={newPatchData.value}
              onChange={(e) => setNewPatchData({ ...newPatchData, value: e.target.value })}
              placeholder="e.g., 1950"
            />
            <TextField
              fullWidth
              label="Reason"
              value={newPatchData.reason}
              onChange={(e) => setNewPatchData({ ...newPatchData, reason: e.target.value })}
              placeholder="e.g., Seasonal markup"
              multiline
              rows={2}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPatchDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleApplyPatch} variant="contained">Apply Patch</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConfigManager;
