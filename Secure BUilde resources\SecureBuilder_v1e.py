
"""
SecureBuilder v1e
-----------------
Single‑file EXE builder with options:
  • unlock wrapper
  • PyArmor obfuscation
  • MSI generation
  • ZIP bundling
"""

import customtkinter as ctk
from tkinter.filedialog import askopenfilename, asksaveasfilename
import subprocess, os, shutil, zipfile

class Builder(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("SecureBuilder v1e")
        self.geometry("780x600")
        self.resizable(False, False)

        # --- variables ---
        self.py_file   = ctk.StringVar()
        self.icon_file = ctk.StringVar()
        self.use_unlock = ctk.BooleanVar(value=False)
        self.use_pyarmor = ctk.BooleanVar(value=False)
        self.use_msi = ctk.BooleanVar(value=False)
        self.zip_output = ctk.BooleanVar(value=True)

        # --- UI ---
        pad = {"padx": 8, "pady": 4}
        ctk.CTkLabel(self, text="Python script").grid(row=0, column=0, **pad, sticky="e")
        ctk.CTkEntry(self, textvariable=self.py_file, width=540).grid(row=0, column=1, **pad)
        ctk.CTkButton(self, text="Browse", command=self.pick_script).grid(row=0, column=2, **pad)

        ctk.CTkLabel(self, text="Icon (.ico)").grid(row=1, column=0, **pad, sticky="e")
        ctk.CTkEntry(self, textvariable=self.icon_file, width=540).grid(row=1, column=1, **pad)
        ctk.CTkButton(self, text="Browse", command=self.pick_icon).grid(row=1, column=2, **pad)

        options = ctk.CTkFrame(self)
        options.grid(row=2, column=0, columnspan=3, pady=10, sticky="ew")
        ctk.CTkCheckBox(options, text="Unlock wrapper", variable=self.use_unlock).pack(anchor="w")
        ctk.CTkCheckBox(options, text="PyArmor obfuscation", variable=self.use_pyarmor).pack(anchor="w")
        ctk.CTkCheckBox(options, text="Generate MSI", variable=self.use_msi).pack(anchor="w")
        ctk.CTkCheckBox(options, text="ZIP bundle", variable=self.zip_output).pack(anchor="w")

        ctk.CTkButton(self, text="BUILD EXE", height=40, command=self.build).grid(
            row=3, column=0, columnspan=3, pady=16, sticky="ew"
        )

        self.log = ctk.CTkTextbox(self, height=10)
        self.log.grid(row=4, column=0, columnspan=3, sticky="nsew", padx=6, pady=6)
        self.grid_rowconfigure(4, weight=1)
        self.grid_columnconfigure(1, weight=1)

    # --- helpers ---
    def pick_script(self):
        path = askopenfilename(filetypes=[("Python", "*.py")])
        if path:
            self.py_file.set(path)

    def pick_icon(self):
        path = askopenfilename(filetypes=[("Icon", "*.ico")])
        if path:
            self.icon_file.set(path)

    def log_line(self, txt):
        self.log.insert("end", txt + "\n")
        self.log.see("end")

    def cmd(self, parts):
        self.log_line("$ " + " ".join(parts))
        subprocess.call(parts, shell=os.name == "nt")

    # --- main build ---
    def build(self):
        script = self.py_file.get()
        if not os.path.exists(script):
            self.log_line("❌ select a valid script")
            return
        base = os.path.splitext(os.path.basename(script))[0]

        build_script = script
        if self.use_pyarmor.get():
            self.log_line("🛡 Obfuscating with PyArmor …")
            self.cmd(["pyarmor", "obfuscate", "--output", "obf", script])
            build_script = os.path.join("obf", os.path.basename(script))

        self.log_line("⚙️  Running PyInstaller")
        cmd = ["pyinstaller", "--noconfirm", "--onefile", build_script]
        if self.icon_file.get():
            cmd += ["--icon", self.icon_file.get()]
        self.cmd(cmd)

        exe = os.path.join("dist", base + ".exe")
        if self.use_unlock.get():
            self.log_line("🔒 Adding unlock wrapper")
            shutil.copy("secure_wrapper_entry.py", "tmp_wrap.py")
            with open("tmp_wrap.py", "a") as f:
                f.write(f'\nimport subprocess,sys;subprocess.call(r"{exe}")\n')
            self.cmd(["pyinstaller", "--onefile", "tmp_wrap.py", "-n", "wrapped_" + base])
            exe = os.path.join("dist", "wrapped_" + base + ".exe")

        if self.use_msi.get():
            self.log_line("📦 Building MSI")
            self.cmd(["python", "msi_wizard_scaffold.py"])

        if self.zip_output.get():
            out_zip = asksaveasfilename(defaultextension=".zip", initialfile=base + "_bundle.zip")
            if out_zip:
                with zipfile.ZipFile(out_zip, "w") as z:
                    for root, _, files in os.walk("dist"):
                        for f in files:
                            fp = os.path.join(root, f)
                            z.write(fp, arcname=os.path.relpath(fp, "dist"))
                self.log_line(f"ZIP written → {out_zip}")

        self.log_line("✅ Build complete")

if __name__ == "__main__":
    Builder().mainloop()
