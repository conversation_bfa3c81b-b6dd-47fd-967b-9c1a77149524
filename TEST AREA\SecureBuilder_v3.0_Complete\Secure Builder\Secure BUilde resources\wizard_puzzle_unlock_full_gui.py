import customtkinter as ctk
class PuzzleUnlock(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("Wizard Unlock")
        self.geometry("300x150")
        self.code = ctk.StringVar()
        ctk.CTkLabel(self, text="Enter Wizard Code").pack(pady=10)
        ctk.CTkEntry(self, textvariable=self.code).pack()
        ctk.CTkButton(self, text="Unlock", command=self.check).pack(pady=10)

    def check(self):
        if self.code.get() == "wizard42":
            ctk.CTkLabel(self, text="✅ Access Granted").pack()
        else:
            ctk.CTkLabel(self, text="❌ Wrong Code").pack()

if __name__ == "__main__":
    PuzzleUnlock().mainloop()
