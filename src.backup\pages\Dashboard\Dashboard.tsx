import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  But<PERSON>,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  FolderOpen as OpenIcon,
  Build as BuildIcon,
  History as HistoryIcon,
  PlayArrow as PlayIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { RootState } from '@/store/store';
import { createNewProject } from '@/store/slices/projectSlice';
import { addNotification } from '@/store/slices/uiSlice';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { currentProject, recentProjects } = useSelector((state: RootState) => state.project);
  const { buildHistory, isBuilding, buildProgress } = useSelector((state: RootState) => state.build);

  const handleNewProject = () => {
    const newProject = {
      name: 'New Project',
      scriptPath: '',
      outputPath: '',
      appName: 'MyApp',
      buildMode: 'clean' as const,
      generateMsi: false,
      generateZip: false,
      enableProtection: false,
      wizardMode: false,
      wizardLevel: 'apprentice' as const,
    };
    
    dispatch(createNewProject(newProject));
    dispatch(addNotification({
      type: 'success',
      title: 'New Project Created',
      message: 'A new project has been created. Configure it in the Project Builder.',
    }));
    navigate('/builder');
  };

  const handleOpenProject = async () => {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog({
          properties: ['openFile'],
          filters: [
            { name: 'SecureBuilder Projects', extensions: ['sbproj'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
          // TODO: Load project from file
          dispatch(addNotification({
            type: 'info',
            title: 'Project Loading',
            message: 'Loading project...',
          }));
        }
      } catch (error) {
        dispatch(addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to open project file.',
        }));
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getBuildModeColor = (mode: string) => {
    switch (mode) {
      case 'clean': return 'default';
      case 'protected': return 'primary';
      case 'obfuscated': return 'secondary';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleNewProject}
                >
                  New Project
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<OpenIcon />}
                  onClick={handleOpenProject}
                >
                  Open Project
                </Button>
                {currentProject && (
                  <Button
                    variant="outlined"
                    startIcon={<BuildIcon />}
                    onClick={() => navigate('/builder')}
                  >
                    Build Current
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Project */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Project
              </Typography>
              {currentProject ? (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    {currentProject.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {currentProject.scriptPath || 'No script selected'}
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip 
                      label={currentProject.buildMode} 
                      color={getBuildModeColor(currentProject.buildMode)}
                      size="small"
                    />
                    {currentProject.enableProtection && (
                      <Chip label="Protected" color="warning" size="small" />
                    )}
                    {currentProject.wizardMode && (
                      <Chip label={`Wizard: ${currentProject.wizardLevel}`} color="info" size="small" />
                    )}
                  </Box>
                  {isBuilding && buildProgress && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        {buildProgress.message}
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={buildProgress.progress} 
                      />
                    </Box>
                  )}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No project loaded. Create a new project or open an existing one.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Projects */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Projects
              </Typography>
              {recentProjects.length > 0 ? (
                <List dense>
                  {recentProjects.slice(0, 5).map((project) => (
                    <ListItem key={project.id}>
                      <ListItemText
                        primary={project.name}
                        secondary={formatDate(project.updatedAt)}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => {
                            // TODO: Load this project
                            dispatch(addNotification({
                              type: 'info',
                              title: 'Loading Project',
                              message: `Loading ${project.name}...`,
                            }));
                          }}
                        >
                          <PlayIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No recent projects found.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Build History */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Build History
              </Typography>
              {buildHistory.length > 0 ? (
                <List dense>
                  {buildHistory.slice(0, 5).map((build) => (
                    <ListItem key={build.id}>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">
                              {build.projectName}
                            </Typography>
                            <Chip
                              label={build.success ? 'Success' : 'Failed'}
                              color={build.success ? 'success' : 'error'}
                              size="small"
                            />
                          </Box>
                        }
                        secondary={formatDate(build.timestamp)}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No builds completed yet.
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
