import os
import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import json
import subprocess
import zipfile
import tempfile
import datetime

PROFILE_EXT = ".sbproj"
SECURE_BACKUP_DIR = os.path.join(os.path.expanduser("~"), "SecureBuilder_Backups")

class SecureBuilderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SecureBuilder v2")
        self.script_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.app_name = tk.StringVar()
        self.splash_path = tk.StringVar()
        self.icon_path = tk.StringVar()
        self.sound_path = tk.StringVar()
        self.build_mode = tk.StringVar(value="Clean Only")
        self.protect_enabled = tk.BooleanVar(value=False)
        self.protected_output_mode = tk.StringVar(value="Secure Only")
        self.generate_msi_var = tk.BooleanVar(value=False)
        self.dev_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_mode_enabled = tk.BooleanVar(value=False)
        self.wizard_level = tk.StringVar(value="Apprentice")
        self.wizard_label_var = tk.StringVar()

        self.create_widgets()

    def create_widgets(self):
        frame = ctk.CTkFrame(self.root)
        frame.pack(padx=20, pady=20)

        padding = dict(padx=5, pady=4)

        ctk.CTkLabel(frame, text="Python Script:").grid(row=0, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.script_path, width=400).grid(row=0, column=1, **padding)
        ctk.CTkButton(frame, text="Browse", command=self.browse_script).grid(row=0, column=2, **padding)

        ctk.CTkLabel(frame, text="Output Folder:").grid(row=1, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.output_folder, width=400).grid(row=1, column=1, **padding)
        ctk.CTkButton(frame, text="Choose", command=self.choose_output_folder).grid(row=1, column=2, **padding)

        ctk.CTkLabel(frame, text="App Name:").grid(row=2, column=0, sticky="w", **padding)
        ctk.CTkEntry(frame, textvariable=self.app_name, width=400).grid(row=2, column=1, columnspan=2, **padding)

        ctk.CTkButton(frame, text="Build EXE", command=self.build_exe).grid(row=10, column=1, pady=10)

        # Wizard Mode toggle
        ctk.CTkCheckBox(frame, text="Enable Wizard Mode", variable=self.wizard_mode_enabled, command=self.toggle_wizard_mode).grid(row=12, column=1, sticky="w", pady=6)
        self.wizard_frame = ctk.CTkFrame(frame)
        self.wizard_frame.grid(row=13, column=0, columnspan=3, pady=6)
        self.wizard_frame.grid_remove()

        ctk.CTkLabel(self.wizard_frame, text="Wizard Level:").grid(row=0, column=0, sticky="w", **padding)
        ctk.CTkOptionMenu(self.wizard_frame, variable=self.wizard_level, values=["Apprentice", "Level 3 Wizard", "Epic Mage"]).grid(row=0, column=1, **padding)

        def update_wizard_icon():
            icon_map = {
                "Apprentice": "🪄 Begin Wizard",
                "Level 3 Wizard": "🎩 Begin Wizard",
                "Epic Mage": "🌌 Begin Wizard"
            }
            self.wizard_label_var.set(icon_map.get(self.wizard_level.get(), "🪄 Begin Wizard"))

        update_wizard_icon()
        self.wizard_level.trace_add("write", lambda *args: update_wizard_icon())
        ctk.CTkButton(self.wizard_frame, textvariable=self.wizard_label_var, command=self.start_wizard).grid(row=1, column=1, pady=6)

    def toggle_wizard_mode(self):
        if self.wizard_mode_enabled.get():
            self.wizard_frame.grid()
        else:
            self.wizard_frame.grid_remove()

    def browse_script(self):
        file_path = filedialog.askopenfilename(filetypes=[("Python Files", "*.py")])
        if file_path:
            self.script_path.set(file_path)

    def choose_output_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_folder.set(folder)

    def build_exe(self):
        script = self.script_path.get()
        appname = self.app_name.get()
        output_dir = self.output_folder.get()
        if not script or not appname:
            messagebox.showwarning("Missing Info", "Script path and App Name are required.")
            return

        build_cmd = ["pyinstaller", "--onefile", "--name", appname, script]
        if self.icon_path.get():
            build_cmd += ["--icon", self.icon_path.get()]
        if self.splash_path.get():
            build_cmd += ["--splash", self.splash_path.get()]

        subprocess.run(build_cmd)
        messagebox.showinfo("Build Complete", "Build finished. Check the 'dist' folder.")

