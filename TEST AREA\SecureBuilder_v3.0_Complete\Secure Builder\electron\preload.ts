import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Store operations
  store: {
    get: (key: string) => ipc<PERSON>enderer.invoke('store-get', key),
    set: (key: string, value: any) => ipc<PERSON>enderer.invoke('store-set', key, value),
    delete: (key: string) => ipcRenderer.invoke('store-delete', key),
  },

  // File system operations
  dialog: {
    showOpenDialog: (options: Electron.OpenDialogOptions) => 
      ipcRenderer.invoke('show-open-dialog', options),
    showSaveDialog: (options: Electron.SaveDialogOptions) => 
      ipcRenderer.invoke('show-save-dialog', options),
  },

  // Command execution
  executeCommand: (command: string, args: string[], cwd?: string) =>
    ipcRenderer.invoke('execute-command', command, args, cwd),

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'menu-new-project',
      'menu-open-project', 
      'menu-save-project',
      'menu-build-exe',
      'menu-clean-build',
      'menu-about',
      'command-output',
      'command-error',
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (_, ...args) => callback(...args));
    }
  },

  // Remove event listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Platform info
  platform: process.platform,
  
  // App version
  version: process.env.npm_package_version || '3.0.0',
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type definitions for TypeScript
export type ElectronAPI = typeof electronAPI;
