# 🚀 SecureBuilder v3.0 - Detailed Launch Guide

## 📋 Prerequisites Checklist

### Required Software
- [ ] **Node.js** (v16.0.0 or higher)
  - Download: https://nodejs.org/
  - Verify: `node --version`
- [ ] **Yarn** (v1.22.0 or higher)
  - Install: `npm install -g yarn`
  - Verify: `yarn --version`
- [ ] **Python** (v3.8+ recommended for PyInstaller)
  - Download: https://python.org/
  - Verify: `python --version`

### System Requirements
- **OS**: Windows 10/11, macOS 10.14+, or Linux
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for dependencies
- **Network**: Internet connection for initial setup

## 🎯 Launch Methods

### Method 1: Automated Launch (Recommended)

1. **Open PowerShell/Terminal** in the project directory
2. **Navigate to project:**
   ```powershell
   cd "TEST AREA\SecureBuilder_v3.0_Complete\Secure Builder"
   ```
3. **Run the start script:**
   ```powershell
   .\start.bat
   ```

### Method 2: Manual Launch (Advanced)

#### Terminal 1 - React Development Server
```powershell
# Navigate to project
cd "TEST AREA\SecureBuilder_v3.0_Complete\Secure Builder"

# Start Vite dev server
node ".\node_modules\vite\bin\vite.js"
```

#### Terminal 2 - Electron Desktop App
```powershell
# Set development environment
$env:NODE_ENV="development"

# Start Electron
node ".\node_modules\electron\cli.js" .
```

### Method 3: Using Package Scripts (If Available)

```powershell
# If npm scripts work in your environment
npm run dev

# Or with yarn
yarn dev
```

## 🔧 Troubleshooting Launch Issues

### Issue: "Command not found" errors

**Symptoms:**
- `'vite' is not recognized as an internal or external command`
- `'concurrently' is not recognized`

**Solutions:**
1. **Use full paths** (as shown in Method 2)
2. **Check node_modules installation:**
   ```powershell
   dir node_modules\.bin\vite*
   ```
3. **Reinstall dependencies if needed:**
   ```powershell
   yarn install
   ```

### Issue: Port conflicts

**Symptoms:**
- `Error: listen EADDRINUSE :::5173`
- Vite fails to start

**Solutions:**
1. **Kill existing processes:**
   ```powershell
   netstat -ano | findstr :5173
   taskkill /PID <PID_NUMBER> /F
   ```
2. **Use different port:**
   ```powershell
   node ".\node_modules\vite\bin\vite.js" --port 5174
   ```

### Issue: Electron fails to load

**Symptoms:**
- Electron window opens but shows error page
- "Failed to load URL" messages

**Solutions:**
1. **Ensure Vite is running first** (Method 2, Terminal 1)
2. **Wait for Vite to be ready** before starting Electron
3. **Check environment variable:**
   ```powershell
   echo $env:NODE_ENV
   ```

### Issue: Path resolution errors

**Symptoms:**
- "Cannot find module" errors
- Wrong directory paths in errors

**Solutions:**
1. **Check for conflicting files** in parent directories
2. **Rename conflicting configs:**
   ```powershell
   ren "..\..\..\vite.config.ts" "..\..\..\vite.config.ts.backup"
   ```

## 📊 Verification Steps

### 1. Verify Vite Server
- [ ] Terminal shows: `VITE v5.4.19 ready in XXX ms`
- [ ] URL displayed: `Local: http://localhost:5173/`
- [ ] No error messages in terminal

### 2. Verify Browser Access
- [ ] Open: http://localhost:5173/
- [ ] Page loads without errors
- [ ] React components render correctly

### 3. Verify Electron Desktop
- [ ] Electron window opens automatically
- [ ] Shows same content as browser
- [ ] DevTools available (Ctrl+Shift+I)
- [ ] Native menu bar present

### 4. Verify Hot Reload
- [ ] Edit a React component file
- [ ] Changes appear instantly in browser
- [ ] Changes appear instantly in Electron

## 🎮 Development Workflow

### Daily Development Routine

1. **Start Development Environment:**
   ```powershell
   cd "TEST AREA\SecureBuilder_v3.0_Complete\Secure Builder"
   .\start.bat
   ```

2. **Open Development Tools:**
   - Browser: F12 or Ctrl+Shift+I
   - Electron: Ctrl+Shift+I (in Electron window)

3. **Make Code Changes:**
   - Edit files in `src/` directory
   - Watch for automatic reload
   - Check console for errors

4. **Test Features:**
   - Use both browser and desktop versions
   - Test IPC communication
   - Verify responsive design

### Code Editing Tips

- **VS Code**: Recommended editor with TypeScript support
- **File Watching**: Changes auto-reload, no manual refresh needed
- **Error Checking**: TypeScript errors show in real-time
- **Debugging**: Use browser DevTools for React debugging

## 🔄 Restart Procedures

### Soft Restart (Recommended)
1. **Refresh browser**: F5 or Ctrl+R
2. **Reload Electron**: Ctrl+R in Electron window

### Hard Restart (If Issues Occur)
1. **Stop all processes**: Ctrl+C in terminals
2. **Clear cache** (if needed):
   ```powershell
   yarn cache clean
   ```
3. **Restart**: Run launch commands again

### Full Reset (Last Resort)
1. **Stop all processes**
2. **Delete node_modules** (if corrupted):
   ```powershell
   rmdir /s node_modules
   ```
3. **Reinstall dependencies**:
   ```powershell
   yarn install
   ```
4. **Restart development environment**

## 📝 Environment Variables

### Development Mode
```powershell
$env:NODE_ENV="development"
```

### Debug Mode (Optional)
```powershell
$env:DEBUG="*"
$env:ELECTRON_ENABLE_LOGGING="true"
```

### Custom Port (If Needed)
```powershell
$env:PORT="5174"
```

## 🎯 Success Indicators

When everything is working correctly, you should see:

1. **Terminal Output:**
   ```
   VITE v5.4.19 ready in XXX ms
   ➜ Local: http://localhost:5173/
   ➜ Network: use --host to expose
   ```

2. **Browser:** SecureBuilder interface loads at http://localhost:5173/

3. **Electron:** Desktop window opens with same interface

4. **No Errors:** Clean console output in both browser and terminals

## 📞 Getting Help

If you encounter issues not covered here:

1. **Check TROUBLESHOOTING.md** for additional solutions
2. **Review terminal output** for specific error messages
3. **Verify all prerequisites** are properly installed
4. **Try the manual launch method** for better error visibility

---

**🎉 Happy Developing with SecureBuilder v3.0!**
