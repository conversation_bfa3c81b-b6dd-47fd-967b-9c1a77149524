{"name": "secure-builder", "version": "3.0.0", "description": "Modern SecureBuilder - Safe, verifiable, and reversible Python executable builder with economic configuration management", "main": "dist/main.js", "homepage": "./", "author": "SecureBuilder Team", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:react\" \"npm run dev:electron\"", "dev:react": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:react && npm run build:electron", "build:react": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux", "test": "vitest", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@reduxjs/toolkit": "^2.0.1", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.8.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "wait-on": "^7.2.0"}, "build": {"appId": "com.securebuilder.app", "productName": "SecureBuilder", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}