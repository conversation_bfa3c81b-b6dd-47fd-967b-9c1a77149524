import tkinter as tk
from tkinter import filedialog, messagebox, Label, Entry, Button, Checkbutton, IntVar, Text, Toplevel
import subprocess
import os
import shutil

class SecureExeBuilder:
    def __init__(self, master):
        self.master = master
        master.title("Secure EXE Builder v1e")
        master.geometry("600x520")

        self.script_path = tk.StringVar()
        self.exe_name = tk.StringVar()
        self.icon_path = tk.StringVar()

        self.use_console = IntVar(value=1)
        self.use_pyarmor = IntVar()
        self.use_flake8 = IntVar()
        self.use_unlock_wrapper = IntVar()
        self.use_msi = IntVar()

        Label(master, text="Python Script").pack()
        Entry(master, textvariable=self.script_path, width=60).pack()
        Button(master, text="Browse", command=self.browse_script).pack()

        Label(master, text="Executable Name").pack()
        Entry(master, textvariable=self.exe_name, width=60).pack()

        Label(master, text="Icon (Optional)").pack()
        Entry(master, textvariable=self.icon_path, width=60).pack()
        Button(master, text="Browse", command=self.browse_icon).pack()

        Checkbutton(master, text="Use Console", variable=self.use_console).pack()
        Checkbutton(master, text="Use PyArmor", variable=self.use_pyarmor).pack()
        Checkbutton(master, text="Run Flake8 Check", variable=self.use_flake8).pack()
        Checkbutton(master, text="Apply Unlock Wrapper", variable=self.use_unlock_wrapper, command=self.enforce_console).pack()
        Checkbutton(master, text="Generate .msi Installer (after EXE)", variable=self.use_msi).pack()

        Button(master, text="Build EXE", command=self.build_exe).pack(pady=5)
        Button(master, text="🧪 Run Original Script", command=self.run_script).pack()
        Button(master, text="🛡️ Run Wrapped Preview", command=self.run_wrapped).pack(pady=5)

        self.status = Label(master, text="", fg="blue")
        self.status.pack()

    def enforce_console(self):
        if self.use_unlock_wrapper.get():
            self.use_console.set(1)

    def browse_script(self):
        path = filedialog.askopenfilename(filetypes=[("Python Files", "*.py")])
        self.script_path.set(path)

    def browse_icon(self):
        path = filedialog.askopenfilename(filetypes=[("Icon Files", "*.ico")])
        self.icon_path.set(path)

    def run_script(self):
        script = self.script_path.get()
        if not os.path.isfile(script):
            self.status.config(text="Invalid script path for run.", fg="red")
            return
        subprocess.Popen(["python", script])

    def run_wrapped(self):
        name = self.exe_name.get() or "SecureApp"
        wrapped_script = f"temp_build/wrapped_{name}.py"
        if os.path.isfile(wrapped_script):
            subprocess.Popen(["python", wrapped_script])
        else:
            self.status.config(text="No wrapped script found. Build first.", fg="red")

    def build_exe(self):
        script = self.script_path.get()
        name = self.exe_name.get() or "SecureApp"
        icon = self.icon_path.get()
        console = self.use_console.get()
        pyarmor = self.use_pyarmor.get()
        flake8 = self.use_flake8.get()
        unlock = self.use_unlock_wrapper.get()
        use_msi = self.use_msi.get()

        if not os.path.isfile(script):
            self.status.config(text="Invalid Python script path.", fg="red")
            return

        os.makedirs("temp_build", exist_ok=True)
        os.makedirs("dist", exist_ok=True)
        os.makedirs("logs", exist_ok=True)

        temp_script = os.path.join("temp_build", f"wrapped_{name}.py")
        shutil.copy(script, temp_script)

        if unlock:
            self.inject_unlock_wrapper(temp_script, name)
            self.create_dev_unlock_tool(name)

        if flake8:
            subprocess.run(["flake8", temp_script], check=False)

        pyi_cmd = ["pyinstaller", "--onefile", f"--name={name}"]
        if not console:
            pyi_cmd.append("--noconsole")
        if icon and os.path.isfile(icon):
            pyi_cmd.append(f"--icon={icon}")
        pyi_cmd.append(temp_script)

        if pyarmor:
            subprocess.run(["pyarmor", "obfuscate", "--output", "temp_build", temp_script], check=False)
            pyi_cmd[-1] = os.path.join("temp_build", os.path.basename(temp_script))

        log_path = os.path.join("logs", f"{name}_buildlog.txt")
        with open(log_path, "w", encoding="utf-8") as log:
            result = subprocess.run(pyi_cmd, stdout=log, stderr=log, text=True)

        if unlock:
            self.build_dev_unlock_exe(name)

        if result.returncode == 0:
            self.status.config(text=f"EXE built: dist/{name}.exe", fg="green")
        else:
            self.status.config(text="Build failed. Check logs.", fg="red")

        if use_msi:
            self.generate_msi(name)

    def inject_unlock_wrapper(self, filepath, app_name):
        with open(filepath, "r+", encoding="utf-8") as f:
            original = f.read()
            f.seek(0)
            f.write(self.unlock_wrapper_code(app_name) + "\n\n" + original)

    def unlock_wrapper_code(self, app_name):
        return f'''
import hashlib, platform, uuid, os
UNLOCK_FILE = "unlock.token"
SECRET_SALT = "SecureWrapperSalt"
APP_SALT = str({len(app_name) * 11 + 17})
def get_machine_id():
    return f"{{platform.node()}}_{{uuid.getnode()}}"
def derive_key(mid, salt):
    return hashlib.sha256((mid + salt + SECRET_SALT).encode()).hexdigest()[:12]
def is_unlocked():
    if not os.path.exists(UNLOCK_FILE): return False
    with open(UNLOCK_FILE) as f: return f.read().strip() == derive_key(get_machine_id(), APP_SALT)
if not is_unlocked():
    print("Validation Code:", get_machine_id() + "__x" + APP_SALT)
    key = input("Enter unlock key: ").strip()
    if key == derive_key(get_machine_id(), APP_SALT):
        with open(UNLOCK_FILE, "w") as f: f.write(key)
    else:
        print("Invalid key."); input("Press Enter to exit."); exit()
'''

    def create_dev_unlock_tool(self, app_name):
        app_salt = str(len(app_name) * 11 + 17)
        output_dir = f"dist_devtools_{app_name}"
        os.makedirs(output_dir, exist_ok=True)
        unlock_script = os.path.join(output_dir, f"generate_unlock_{app_name}.py")

        with open(unlock_script, "w") as f:
            f.write(f'''
import hashlib
SECRET_SALT = "SecureWrapperSalt"
def derive_key(mid, salt):
    return hashlib.sha256((mid + salt + SECRET_SALT).encode()).hexdigest()[:12]

print("Unlock Tool for {app_name}")
code = input("Paste validation code: ").strip()
if "__x" not in code:
    print("Invalid format.")
else:
    mid, salt = code.split("__x")
    print("Unlock Key:", derive_key(mid, salt))
input("Press Enter to exit...")
''')

        with open(os.path.join(output_dir, f"Unlock_README_{app_name}.txt"), "w") as f:
            f.write(f"Unlock tool for: {app_name}\nSend user the unlock key after validation.")

    def build_dev_unlock_exe(self, app_name):
        script_path = f"dist_devtools_{app_name}/generate_unlock_{app_name}.py"
        if os.path.exists(script_path):
            subprocess.run([
                "pyinstaller", "--onefile", "--name", f"UnlockGenerator_{app_name}",
                script_path
            ])

    def generate_msi(self, app_name):
        os.makedirs("dist_msi", exist_ok=True)
        exe_path = f"dist/{app_name}.exe"
        msi_path = f"dist_msi/{app_name}.msi"
        if not os.path.exists(exe_path):
            print("EXE not found for MSI packaging.")
            return
        try:
            subprocess.run(["msi-packager", exe_path, "--name", app_name, "--output", msi_path], check=True)
            print(f"✅ MSI created: {msi_path}")
        except Exception as e:
            print(f"❌ Failed to create MSI: {e}")

root = tk.Tk()
app = SecureExeBuilder(root)
root.mainloop()